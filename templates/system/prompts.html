{% extends "base.html" %}

{% block title %}提示词管理 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/aitest">首页</a></li>
<li class="breadcrumb-item active">提示词管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-file-alt me-2"></i>提示词管理
            </h1>
            <button class="btn btn-primary" onclick="createPrompt()">
                <i class="fas fa-plus me-1"></i>新建提示词
            </button>
        </div>
    </div>
</div>

<!-- 测试用例类型提示词管理 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-vial me-2"></i>测试用例生成提示词
                </h5>
                <p class="text-muted">管理不同类型测试用例的生成提示词，确保每种类型生成专门的测试用例。</p>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>测试类型</th>
                                <th>名称</th>
                                <th>描述</th>
                                <th>文件名</th>
                                <th>修改时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="testPromptsTableBody">
                            <!-- 测试提示词列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 其他提示词管理 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-file-code me-2"></i>其他提示词
                </h5>
                <p class="text-muted">管理需求提取、自动化测试等其他功能的提示词。</p>
                
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>文件名</th>
                                <th>名称</th>
                                <th>大小</th>
                                <th>修改时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="otherPromptsTableBody">
                            <!-- 其他提示词列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 编辑提示词模态框 -->
<div class="modal fade" id="promptModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="promptModalTitle">编辑提示词</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="promptForm">
                    <input type="hidden" id="promptFilename">
                    <input type="hidden" id="promptType">
                    
                    <div class="mb-3">
                        <label for="promptName" class="form-label">提示词名称</label>
                        <input type="text" class="form-control" id="promptName" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="promptDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="promptDescription" rows="2"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="promptContent" class="form-label">提示词内容</label>
                        <textarea class="form-control" id="promptContent" rows="20" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="savePrompt()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 新建提示词模态框 -->
<div class="modal fade" id="createPromptModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">新建提示词</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createPromptForm">
                    <div class="mb-3">
                        <label for="newPromptType" class="form-label">提示词类型</label>
                        <select class="form-select" id="newPromptType" required>
                            <option value="">请选择类型</option>
                            <option value="test">测试用例生成</option>
                            <option value="other">其他功能</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="testTypeGroup" style="display: none;">
                        <label for="newTestType" class="form-label">测试类型</label>
                        <select class="form-select" id="newTestType">
                            <option value="">请选择测试类型</option>
                            <option value="functional">功能测试</option>
                            <option value="api">接口测试</option>
                            <option value="system">系统测试</option>
                            <option value="performance">性能测试</option>
                            <option value="security">安全测试</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="newPromptFilename" class="form-label">文件名</label>
                        <input type="text" class="form-control" id="newPromptFilename" required>
                        <div class="form-text">请输入文件名，如：custom_test_generation.md</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="newPromptName" class="form-label">提示词名称</label>
                        <input type="text" class="form-control" id="newPromptName" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="newPromptDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="newPromptDescription" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createNewPrompt()">创建</button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
let testCaseTypes = {};

$(document).ready(function() {
    loadTestCaseTypes();
    loadTestPrompts();
    loadOtherPrompts();
    
    // 监听提示词类型变化
    $('#newPromptType').change(function() {
        const type = $(this).val();
        if (type === 'test') {
            $('#testTypeGroup').show();
        } else {
            $('#testTypeGroup').hide();
        }
    });
});

// 加载测试用例类型配置
function loadTestCaseTypes() {
    $.get('/aitest/system/api/test_case_types', function(response) {
        if (response.success) {
            testCaseTypes = response.data;
        }
    });
}

// 加载测试提示词列表
function loadTestPrompts() {
    $.get('/aitest/system/api/prompts', function(response) {
        if (response.success) {
            renderTestPromptsTable(response.data);
        } else {
            showNotification('加载提示词列表失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载提示词列表失败', 'error');
    });
}

// 渲染测试提示词表格
function renderTestPromptsTable(prompts) {
    const tbody = $('#testPromptsTableBody');
    tbody.empty();
    
    // 测试类型的提示词文件
    const testPromptFiles = ['functional_test_generation.md', 'api_test_generation.md', 'system_test_generation.md', 'performance_test_generation.md', 'security_test_generation.md'];
    
    // 过滤出测试类型的提示词
    const testPrompts = prompts.filter(p => testPromptFiles.includes(p.filename));
    
    if (testPrompts.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="6" class="text-center text-muted">暂无测试提示词</td>
            </tr>
        `);
        return;
    }

    // 预定义的测试类型信息
    const typesInfo = {
        'functional_test_generation.md': { key: 'functional', name: '功能测试', description: '通过界面验证功能的操作步骤、预期结果等' },
        'api_test_generation.md': { key: 'api', name: '接口测试', description: '通过API验证功能的操作步骤、预期结果等' },
        'system_test_generation.md': { key: 'system', name: '系统测试', description: '执行系统命令，验证功能点是否正常' },
        'performance_test_generation.md': { key: 'performance', name: '性能测试', description: '根据功能点生成性能测试用例' },
        'security_test_generation.md': { key: 'security', name: '安全测试', description: '根据功能点生成安全测试用例' }
    };

    testPrompts.forEach(function(prompt) {
        const typeInfo = typesInfo[prompt.filename] || { key: 'unknown', name: prompt.name, description: '' };
        const modifiedDate = new Date(prompt.modified_at * 1000).toLocaleString();
        const fileSize = (prompt.size / 1024).toFixed(2) + ' KB';

        const row = $(`
            <tr>
                <td>
                    <span class="badge bg-primary">${typeInfo.key.toUpperCase()}</span>
                </td>
                <td><strong>${typeInfo.name}</strong></td>
                <td><small class="text-muted">${typeInfo.description}</small></td>
                <td><code>${prompt.filename}</code></td>
                <td><small class="text-muted">${modifiedDate}</small></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editTestPrompt('${typeInfo.key}', '${prompt.filename}', '${typeInfo.name}')" title="编辑">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 加载其他提示词列表
function loadOtherPrompts() {
    $.get('/aitest/system/api/prompts', function(response) {
        if (response.success) {
            renderOtherPromptsTable(response.data);
        } else {
            showNotification('加载提示词列表失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载提示词列表失败', 'error');
    });
}

// 渲染其他提示词表格
function renderOtherPromptsTable(prompts) {
    const tbody = $('#otherPromptsTableBody');
    tbody.empty();
    
    // 过滤掉测试类型的提示词
    const testPromptFiles = ['ui_test_generation.md', 'api_test_generation.md', 'system_test_generation.md', 'performance_test_generation.md', 'security_test_generation.md'];
    const otherPrompts = prompts.filter(p => !testPromptFiles.includes(p.filename));
    
    if (otherPrompts.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="5" class="text-center text-muted">暂无其他提示词</td>
            </tr>
        `);
        return;
    }

    otherPrompts.forEach(function(prompt) {
        const modifiedDate = new Date(prompt.modified_at * 1000).toLocaleString();
        const fileSize = (prompt.size / 1024).toFixed(2) + ' KB';

        const row = $(`
            <tr>
                <td><code>${prompt.filename}</code></td>
                <td><strong>${prompt.name}</strong></td>
                <td>${fileSize}</td>
                <td><small class="text-muted">${modifiedDate}</small></td>
                <td>
                    <button class="btn btn-sm btn-outline-primary" onclick="editOtherPrompt('${prompt.filename}', '${prompt.name}')" title="编辑">
                        <i class="fas fa-edit"></i> 编辑
                    </button>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 编辑测试提示词
function editTestPrompt(testType, filename, name) {
    $('#promptModalTitle').text(`编辑${name}提示词`);
    $('#promptFilename').val(filename);
    $('#promptType').val('test');
    $('#promptName').val(name);
    $('#promptDescription').val(testCaseTypes[testType]?.description || '');

    $.get(`/aitest/system/api/prompts/${filename}`, function(response) {
        if (response.success) {
            $('#promptContent').val(response.data.content);
            const modal = new bootstrap.Modal($('#promptModal')[0]);
            modal.show();
        } else {
            showNotification('加载提示词内容失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载提示词内容失败', 'error');
    });
}

// 编辑其他提示词
function editOtherPrompt(filename, name) {
    $('#promptModalTitle').text(`编辑提示词 - ${name}`);
    $('#promptFilename').val(filename);
    $('#promptType').val('other');
    $('#promptName').val(name);
    $('#promptDescription').val('');

    $.get(`/aitest/system/api/prompts/${filename}`, function(response) {
        if (response.success) {
            $('#promptContent').val(response.data.content);
            const modal = new bootstrap.Modal($('#promptModal')[0]);
            modal.show();
        } else {
            showNotification('加载提示词内容失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载提示词内容失败', 'error');
    });
}

// 保存提示词
function savePrompt() {
    const filename = $('#promptFilename').val();
    const content = $('#promptContent').val();

    if (!content.trim()) {
        showNotification('提示词内容不能为空', 'error');
        return;
    }

    $.ajax({
        url: `/aitest/system/api/prompts/${filename}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({ content: content }),
        success: function(response) {
            if (response.success) {
                showNotification('提示词保存成功', 'success');
                $('#promptModal').modal('hide');
                loadTestPrompts(); // 重新加载列表
                loadOtherPrompts(); // 重新加载列表
            } else {
                showNotification('保存失败: ' + response.message, 'error');
            }
        },
        error: function() {
            showNotification('保存失败', 'error');
        }
    });
}

// 创建提示词
function createPrompt() {
    $('#createPromptForm')[0].reset();
    $('#testTypeGroup').hide();
    const modal = new bootstrap.Modal($('#createPromptModal')[0]);
    modal.show();
}

// 创建新提示词
function createNewPrompt() {
    const type = $('#newPromptType').val();
    const testType = $('#newTestType').val();
    const filename = $('#newPromptFilename').val();
    const name = $('#newPromptName').val();
    const description = $('#newPromptDescription').val();

    if (!type || !filename || !name) {
        showNotification('请填写必填字段', 'error');
        return;
    }

    if (type === 'test' && !testType) {
        showNotification('请选择测试类型', 'error');
        return;
    }

    // 构建初始内容
    let content = `# 角色\n你是一名专业的测试工程师。\n\n# 目标\n${description}\n\n# 输出格式\n请严格以JSON格式返回结果。`;

    $.ajax({
        url: `/aitest/system/api/prompts/${filename}`,
        method: 'PUT',
        contentType: 'application/json',
        data: JSON.stringify({ content: content }),
        success: function(response) {
            if (response.success) {
                showNotification('提示词创建成功', 'success');
                $('#createPromptModal').modal('hide');
                loadTestPrompts(); // 重新加载测试提示词列表
                loadOtherPrompts(); // 重新加载其他提示词列表
            } else {
                showNotification('创建失败: ' + response.message, 'error');
            }
        },
        error: function() {
            showNotification('创建失败', 'error');
        }
    });
}
</script>
{% endblock %}