{% extends "base.html" %}

{% block title %}项目管理 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/aitest">首页</a></li>
<li class="breadcrumb-item active">项目管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-project-diagram me-2"></i>项目管理
            </h1>
            <button class="btn btn-primary" onclick="showCreateModal()">
                <i class="fas fa-plus me-1"></i>创建项目
            </button>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="search-box">
            <input type="text" class="form-control" id="searchInput" placeholder="搜索项目名称或描述...">
            <i class="fas fa-search search-icon"></i>
        </div>
    </div>
    <div class="col-md-6 text-end">
        <button class="btn btn-outline-secondary" onclick="refreshProjects()">
            <i class="fas fa-sync me-1"></i>刷新
        </button>
    </div>
</div>

<!-- 项目列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>描述</th>
                                <th>统计信息</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="projectsTableBody">
                            <!-- 项目列表将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <div id="pagination"></div>
            </div>
        </div>
    </div>
</div>

<!-- 创建/编辑项目模态框 -->
<div class="modal fade" id="projectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="projectModalTitle">创建项目</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="projectForm">
                    <input type="hidden" id="projectId">
                    <div class="mb-3">
                        <label for="projectName" class="form-label">项目名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="projectName" required>
                        <div class="invalid-feedback" id="nameError"></div>
                    </div>
                    <div class="mb-3">
                        <label for="projectDescription" class="form-label">项目描述</label>
                        <textarea class="form-control" id="projectDescription" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveProject()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 项目详情模态框 -->
<div class="modal fade" id="projectDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">项目详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <h6 id="detailProjectName">项目名称</h6>
                        <p class="text-muted" id="detailProjectDescription">项目描述</p>
                        <small class="text-muted">
                            创建时间: <span id="detailProjectCreatedAt">-</span><br>
                            更新时间: <span id="detailProjectUpdatedAt">-</span><br>
                            项目ID: <span id="detailProjectID">-</span>
                        </small>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">统计信息</h6>
                                <ul class="list-unstyled mb-0">
                                    <li>文档: <span id="detailDocumentsCount">0</span></li>
                                    <li>需求: <span id="detailRequirementsCount">0</span></li>
                                    <li>测试用例: <span id="detailTestCasesCount">0</span></li>
                                    <li>知识库: <span id="detailKnowledgeItemsCount">0</span></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="selectProjectFromDetail()">选择此项目</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentSearch = '';
let selectedProjectId = null;

$(document).ready(function() {
    loadProjects();
    
    // 搜索输入框事件
    $('#searchInput').on('input', debounce(function() {
        currentSearch = $(this).val();
        currentPage = 1;
        loadProjects();
    }, 500));
});

// 加载项目列表
function loadProjects() {
    const params = {
        page: currentPage,
        page_size: 10
    };
    
    if (currentSearch) {
        params.search = currentSearch;
    }
    
    $.get('/aitest/projects/api/list', params, function(response) {
        if (response.success) {
            renderProjectsTable(response.data.records);
            initPagination('pagination', response.data.total_pages, currentPage, function(page) {
                currentPage = page;
                loadProjects();
            });
        } else {
            showNotification('加载项目列表失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('加载项目列表失败', 'error');
    });
}

// 渲染项目表格
function renderProjectsTable(projects) {
    const tbody = $('#projectsTableBody');
    tbody.empty();
    
    if (projects.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="5" class="text-center text-muted py-4">
                    <i class="fas fa-folder-open fa-2x mb-2"></i><br>
                    暂无项目数据
                </td>
            </tr>
        `);
        return;
    }
    
    projects.forEach(function(project) {
        const stats = project.stats || {};
        const row = $(`
            <tr>
                <td>
                    <strong>${project.name}</strong>
                </td>
                <td>
                    <span class="text-muted">${project.description || '暂无描述'}</span>
                </td>
                <td>
                    <small>
                        文档: ${stats.documents || 0} | 
                        需求: ${stats.requirements || 0} | 
                        测试: ${stats.test_cases || 0}
                    </small>
                </td>
                <td>
                    <small class="text-muted">${formatDate(project.created_at)}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="viewProject('${project.id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="selectProject('${project.id}', '${project.name}')" title="选择项目">
                            <i class="fas fa-check"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editProject('${project.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteProject('${project.id}', '${project.name}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 显示创建模态框
function showCreateModal() {
    $('#projectModalTitle').text('创建项目');
    $('#projectForm')[0].reset();
    $('#projectId').val('');
    $('#projectName').removeClass('is-invalid');
    const modal = new bootstrap.Modal($('#projectModal')[0]);
    modal.show();
}

// 编辑项目
function editProject(projectId) {
    $.get(`/aitest/projects/api/${projectId}`, function(response) {
        if (response.success) {
            const project = response.data;
            $('#projectModalTitle').text('编辑项目');
            $('#projectId').val(project.id);
            $('#projectName').val(project.name);
            $('#projectDescription').val(project.description || '');
            $('#projectName').removeClass('is-invalid');
            
            const modal = new bootstrap.Modal($('#projectModal')[0]);
            modal.show();
        } else {
            showNotification('获取项目信息失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('获取项目信息失败', 'error');
    });
}

// 保存项目
function saveProject() {
    const projectId = $('#projectId').val();
    const name = $('#projectName').val().trim();
    const description = $('#projectDescription').val().trim();
    
    if (!name) {
        $('#projectName').addClass('is-invalid');
        $('#nameError').text('项目名称不能为空');
        return;
    }
    
    const data = {
        name: name,
        description: description
    };
    
    const url = projectId ? `/aitest/projects/api/${projectId}/update` : '/aitest/projects/api/create';
    const method = projectId ? 'PUT' : 'POST';
    
    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                bootstrap.Modal.getInstance($('#projectModal')[0]).hide();
                loadProjects();
                loadProjects(); // 刷新顶部项目列表
            } else {
                if (response.message.includes('名称')) {
                    $('#projectName').addClass('is-invalid');
                    $('#nameError').text(response.message);
                } else {
                    showNotification(response.message, 'error');
                }
            }
        },
        error: function() {
            showNotification('保存项目失败', 'error');
        }
    });
}

// 查看项目详情
function viewProject(projectId) {
    $.get(`/aitest/projects/api/${projectId}`, function(response) {
        if (response.success) {
            const project = response.data;
            const stats = project.stats || {};
            
            $('#detailProjectName').text(project.name);
            $('#detailProjectID').text(projectId);            
            $('#detailProjectDescription').text(project.description || '暂无描述');
            $('#detailProjectCreatedAt').text(formatDate(project.created_at));
            $('#detailProjectUpdatedAt').text(formatDate(project.updated_at));
            $('#detailDocumentsCount').text(stats.documents || 0);
            $('#detailRequirementsCount').text(stats.requirements || 0);
            $('#detailTestCasesCount').text(stats.test_cases || 0);
            $('#detailKnowledgeItemsCount').text(stats.knowledge_items || 0);
            
            selectedProjectId = project.id;
            
            const modal = new bootstrap.Modal($('#projectDetailModal')[0]);
            modal.show();
        } else {
            showNotification('获取项目详情失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('获取项目详情失败', 'error');
    });
}

// 从详情页选择项目
function selectProjectFromDetail() {
    if (selectedProjectId) {
        const projectName = $('#detailProjectName').text();
        selectProject(selectedProjectId, projectName);
        bootstrap.Modal.getInstance($('#projectDetailModal')[0]).hide();
    }
}

// 删除项目
function deleteProject(projectId, projectName) {
    if (confirm(`确定要删除项目"${projectName}"吗？此操作将删除项目下的所有数据，且不可恢复。`)) {
        $.ajax({
            url: `/aitest/projects/api/${projectId}/delete`,
            method: 'DELETE',
            success: function(response) {
                if (response.success) {
                    showNotification(response.message, 'success');
                    loadProjects();
                    loadProjects(); // 刷新顶部项目列表
                } else {
                    showNotification(response.message, 'error');
                }
            },
            error: function() {
                showNotification('删除项目失败', 'error');
            }
        });
    }
}

// 刷新项目列表
function refreshProjects() {
    currentPage = 1;
    currentSearch = '';
    $('#searchInput').val('');
    loadProjects();
}

// 刷新页面数据
function refreshPageData() {
    loadProjects();
}
</script>
{% endblock %}
