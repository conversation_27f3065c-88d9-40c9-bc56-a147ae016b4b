{% extends "base.html" %}

{% block title %}接口项目管理 - AI智能测试平台{% endblock %}

{% block breadcrumb %}
<li class="breadcrumb-item"><a href="/aitest">首页</a></li>
<li class="breadcrumb-item active">接口项目管理</li>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-code me-2"></i>接口项目管理
            </h1>
            <button class="btn btn-primary" onclick="showCreateModal()">
                <i class="fas fa-plus me-1"></i>新建项目
            </button>
        </div>
    </div>
</div>

<!-- 搜索和过滤 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="input-group">
            <input type="text" class="form-control" id="searchInput" placeholder="搜索项目名称或描述...">
            <button class="btn btn-outline-secondary" type="button" onclick="searchProjects()">
                <i class="fas fa-search"></i>
            </button>
        </div>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group">
            <button class="btn btn-outline-secondary" onclick="refreshProjects()">
                <i class="fas fa-sync me-1"></i>刷新
            </button>
        </div>
    </div>
</div>

<!-- 项目列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>项目列表
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>项目名称</th>
                                <th>描述</th>
                                <th>开发语言</th>
                                <th>状态</th>
                                <th>文档</th>
                                <th>文件数</th>
                                <th>创建时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="projectsTableBody">
                            <tr>
                                <td colspan="8" class="text-center text-muted py-4">
                                    <i class="fas fa-spinner fa-spin fa-2x mb-2"></i><br>
                                    加载中...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="项目分页">
                    <ul class="pagination justify-content-center" id="pagination">
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 创建/编辑项目模态框 -->
<div class="modal fade" id="projectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="projectModalTitle">新建接口项目</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="projectForm">
                    <input type="hidden" id="projectId">
                    <div class="mb-3">
                        <label for="projectName" class="form-label">项目名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="projectName" required>
                    </div>
                    <!-- 文档上传部分 -->
                    <div class="mb-3" id="documentUploadSection">
                        <label for="projectDocument" class="form-label">接口文档 (可选)</label>
                        <input type="file" class="form-control" id="projectDocument" accept=".doc,.docx,.pdf">
                        <div class="form-text">支持 Word (.doc, .docx) 和 PDF (.pdf) 格式，上传后将自动使用文档名称作为项目名称</div>
                    </div>
                    <div class="mb-3">
                        <label for="projectLanguage" class="form-label">开发语言</label>
                        <select class="form-select" id="projectLanguage">
                            <option value="Java">Java</option>
                            <option value="Python">Python</option>
                            <option value="JavaScript">JavaScript</option>
                            <option value="C#">C#</option>
                            <option value="Go">Go</option>
                            <option value="PHP">PHP</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="projectDescription" class="form-label">项目描述</label>
                        <textarea class="form-control" id="projectDescription" rows="2"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="projectStatus" class="form-label">项目状态</label>
                        <select class="form-select" id="projectStatus">
                            <option value="待开发">待开发</option>
                            <option value="开发中">开发中</option>
                            <option value="测试中">测试中</option>
                            <option value="已完成">已完成</option>
                            <option value="已暂停">已暂停</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="saveProject()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 上传文档模态框 -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">上传接口文档</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <input type="hidden" id="uploadProjectId">
                    <div class="mb-3">
                        <label for="documentFile" class="form-label">选择文档 <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="documentFile" accept=".doc,.docx,.pdf" required>
                        <div class="form-text">支持 Word (.doc, .docx) 和 PDF (.pdf) 格式</div>
                    </div>
                    <div class="mb-3">
                        <label for="customFilename" class="form-label">自定义文件名（可选）</label>
                        <input type="text" class="form-control" id="customFilename" placeholder="留空则使用原文件名">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="uploadDocument()">上传</button>
            </div>
        </div>
    </div>
</div>

<!-- 项目详情模态框 -->
<div class="modal fade" id="detailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">项目详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="projectDetails">
                    <!-- 项目详情内容将在这里动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentPage = 1;
let currentSearch = '';

$(document).ready(function() {
    loadProjects();

    // 搜索输入框事件
    $('#searchInput').on('input', debounce(function() {
        searchProjects();
    }, 500));

    // 回车搜索
    $('#searchInput').on('keypress', function(e) {
        if (e.which === 13) {
            searchProjects();
        }
    });

    // 文档选择事件 - 自动填充项目名称
    $(document).on('change', '#projectDocument', function() {
        const file = this.files[0];
        if (file && !$('#projectName').val()) {
            const fileName = file.name;
            const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
            $('#projectName').val(nameWithoutExt);
        }
    });
});

// 加载项目列表
function loadProjects() {
    console.log('Loading projects...', {page: currentPage, search: currentSearch});

    $.get('/aitest/api-projects/api/list', {
        page: currentPage,
        page_size: 20,
        search: currentSearch
    }, function(response) {
        console.log('Projects response:', response);

        if (response.success) {
            const records = response.data.records || [];
            console.log('Rendering projects:', records.length, 'records');
            renderProjectsTable(records);
            initPagination('pagination', response.data.total_pages, currentPage, function(page) {
                currentPage = page;
                loadProjects();
            });
        } else {
            console.error('Load projects failed:', response.message);
            showNotification('加载项目列表失败: ' + response.message, 'error');
        }
    }).fail(function(xhr, status, error) {
        console.error('Load projects request failed:', xhr, status, error);
        showNotification('加载项目列表失败: ' + error, 'error');
    });
}

// 渲染项目表格
function renderProjectsTable(projects) {
    const tbody = $('#projectsTableBody');
    tbody.empty();
    
    if (projects.length === 0) {
        tbody.append(`
            <tr>
                <td colspan="8" class="text-center text-muted py-4">
                    <i class="fas fa-folder-open fa-2x mb-2"></i><br>
                    暂无项目数据
                </td>
            </tr>
        `);
        return;
    }
    
    projects.forEach(function(project) {
        const row = $(`
            <tr>
                <td>
                    <a href="javascript:void(0)" onclick="viewProjectDetail('${project.id}')" class="text-decoration-none">
                        <strong>${project.name || '未命名项目'}</strong>
                    </a>
                </td>
                <td>
                    <div style="max-width: 200px; overflow: hidden; text-overflow: ellipsis;">
                        ${project.description || '-'}
                    </div>
                </td>
                <td>
                    <span class="badge bg-info">${project.language || 'Java'}</span>
                </td>
                <td>${getStatusBadge(project.status)}</td>
                <td>
                    ${project.document_name ? 
                        `<i class="fas fa-file-alt text-success" title="${project.document_name}"></i>` : 
                        '<i class="fas fa-file-alt text-muted" title="未上传"></i>'
                    }
                </td>
                <td>
                    <span class="badge bg-secondary">${project.statistics?.total_files || 0}</span>
                </td>
                <td>
                    <small class="text-muted">${formatDate(project.created_at)}</small>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="viewProjectDetail('${project.id}')" title="查看详情">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="showUploadModal('${project.id}')" title="上传文档">
                            <i class="fas fa-upload"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="generateTestCode('${project.id}')" title="生成测试代码">
                            <i class="fas fa-code"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="downloadProject('${project.id}')" title="下载项目">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="editProject('${project.id}')" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteProject('${project.id}', '${project.name}')" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `);
        tbody.append(row);
    });
}

// 搜索项目
function searchProjects() {
    currentSearch = $('#searchInput').val().trim();
    currentPage = 1;
    loadProjects();
}

// 刷新项目列表
function refreshProjects() {
    currentPage = 1;
    currentSearch = '';
    $('#searchInput').val('');
    loadProjects();
}

// 显示创建项目模态框
function showCreateModal() {
    $('#projectModalTitle').text('新建接口项目');
    $('#projectForm')[0].reset();
    $('#projectId').val('');
    $('#projectDocument').val(''); // 清空文档选择
    $('#projectModal').modal('show');
}

// 编辑项目
function editProject(projectId) {
    $.get(`/aitest/api-projects/api/${projectId}`, function(response) {
        if (response.success) {
            const project = response.data;
            $('#projectModalTitle').text('编辑接口项目');
            $('#projectId').val(project.id);
            $('#projectName').val(project.name);
            $('#projectDescription').val(project.description);
            $('#projectLanguage').val(project.language);
            $('#projectStatus').val(project.status);
            $('#projectModal').modal('show');
        } else {
            showNotification('获取项目信息失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('获取项目信息失败', 'error');
    });
}

// 保存项目
function saveProject() {
    const projectId = $('#projectId').val();
    const documentFile = $('#projectDocument')[0].files[0];

    // 如果有文档上传且没有项目名称，使用文档名称
    if (documentFile && !$('#projectName').val()) {
        const fileName = documentFile.name;
        const nameWithoutExt = fileName.substring(0, fileName.lastIndexOf('.')) || fileName;
        $('#projectName').val(nameWithoutExt);
    }

    const data = {
        name: $('#projectName').val(),
        description: $('#projectDescription').val(),
        language: $('#projectLanguage').val(),
        status: $('#projectStatus').val()
    };

    if (!data.name) {
        showNotification('项目名称不能为空', 'error');
        return;
    }

    const url = projectId ?
        `/aitest/api-projects/api/${projectId}/update` :
        '/aitest/api-projects/api/create';
    const method = projectId ? 'PUT' : 'POST';

    console.log('Saving project:', data, 'URL:', url, 'Method:', method);

    // 先创建项目
    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify(data),
        success: function(response) {
            console.log('Save project response:', response);

            if (response.success) {
                const createdProjectId = projectId || response.data.id;
                console.log('Project created with ID:', createdProjectId);

                // 如果有文档，上传文档
                if (documentFile) {
                    console.log('Uploading document for project:', createdProjectId);
                    uploadDocumentForProject(createdProjectId, documentFile, function(uploadSuccess) {
                        if (uploadSuccess) {
                            showNotification('项目创建并上传文档成功', 'success');
                        } else {
                            showNotification('项目创建成功，但文档上传失败', 'warning');
                        }
                        $('#projectModal').modal('hide');
                        // 延迟刷新以确保数据已保存
                        setTimeout(function() {
                            loadProjects();
                        }, 500);
                    });
                } else {
                    showNotification(response.message, 'success');
                    $('#projectModal').modal('hide');
                    // 延迟刷新以确保数据已保存
                    setTimeout(function() {
                        loadProjects();
                    }, 500);
                }
            } else {
                console.error('Save project failed:', response.message);
                showNotification(response.message, 'error');
            }
        },
        error: function(xhr, status, error) {
            console.error('Save project request failed:', xhr, status, error);
            showNotification('保存项目失败: ' + error, 'error');
        }
    });
}

// 为项目上传文档的辅助函数
function uploadDocumentForProject(projectId, file, callback) {
    const formData = new FormData();
    formData.append('file', file);

    $.ajax({
        url: `/aitest/api-projects/api/${projectId}/upload`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            callback(response.success);
        },
        error: function() {
            callback(false);
        }
    });
}

// 删除项目
function deleteProject(projectId, projectName) {
    if (!confirm(`确定要删除项目"${projectName}"吗？此操作将删除项目及其所有文件，不可恢复。`)) {
        return;
    }

    $.ajax({
        url: `/aitest/api-projects/api/${projectId}/delete`,
        method: 'DELETE',
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                loadProjects();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('删除项目失败', 'error');
        }
    });
}

// 显示上传文档模态框
function showUploadModal(projectId) {
    $('#uploadProjectId').val(projectId);
    $('#uploadForm')[0].reset();
    $('#uploadModal').modal('show');
}

// 上传文档
function uploadDocument() {
    const projectId = $('#uploadProjectId').val();
    const fileInput = $('#documentFile')[0];
    const customFilename = $('#customFilename').val();

    if (!fileInput.files.length) {
        showNotification('请选择要上传的文件', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    if (customFilename) {
        formData.append('filename', customFilename);
    }

    $.ajax({
        url: `/aitest/api-projects/api/${projectId}/upload`,
        method: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                showNotification(response.message, 'success');
                $('#uploadModal').modal('hide');
                loadProjects();
            } else {
                showNotification(response.message, 'error');
            }
        },
        error: function() {
            showNotification('上传文档失败', 'error');
        }
    });
}

// 查看项目详情
function viewProjectDetail(projectId) {
    $.get(`/aitest/api-projects/api/${projectId}`, function(response) {
        if (response.success) {
            const project = response.data;
            const detailsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>项目名称:</strong></td><td>${project.name}</td></tr>
                            <tr><td><strong>开发语言:</strong></td><td>${project.language}</td></tr>
                            <tr><td><strong>状态:</strong></td><td>${getStatusBadge(project.status)}</td></tr>
                            <tr><td><strong>创建时间:</strong></td><td>${formatDate(project.created_at)}</td></tr>
                            <tr><td><strong>更新时间:</strong></td><td>${formatDate(project.updated_at)}</td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>统计信息</h6>
                        <table class="table table-sm">
                            <tr><td><strong>总文件数:</strong></td><td>${project.statistics.total_files}</td></tr>
                            <tr><td><strong>测试文件:</strong></td><td>${project.statistics.test_files}</td></tr>
                            <tr><td><strong>文档文件:</strong></td><td>${project.statistics.document_files}</td></tr>
                            <tr><td><strong>总大小:</strong></td><td>${formatFileSize(project.statistics.total_size)}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>项目描述</h6>
                        <p>${project.description || '暂无描述'}</p>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>项目文件</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>大小</th>
                                        <th>修改时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${project.files.length > 0 ?
                                        project.files.map(file => `
                                            <tr>
                                                <td>${file.name}</td>
                                                <td>${formatFileSize(file.size)}</td>
                                                <td>${formatDate(file.modified_at)}</td>
                                            </tr>
                                        `).join('') :
                                        '<tr><td colspan="3" class="text-center text-muted">暂无文件</td></tr>'
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            $('#projectDetails').html(detailsHtml);
            $('#detailModal').modal('show');
        } else {
            showNotification('获取项目详情失败: ' + response.message, 'error');
        }
    }).fail(function() {
        showNotification('获取项目详情失败', 'error');
    });
}

// 下载项目
function downloadProject(projectId) {
    window.open(`/aitest/api-projects/api/${projectId}/download`, '_blank');
}

// 生成测试代码
function generateTestCode(projectId) {
    if (!confirm('确定要生成测试代码吗？这可能需要几分钟时间。')) {
        return;
    }

    // 先解析接口文档
    $.post(`/aitest/api-projects/api/${projectId}/parse`, function(parseResponse) {
        if (parseResponse.success) {
            if (parseResponse.interfaces.length === 0) {
                showNotification('未找到接口信息，请检查文档格式', 'warning');
                return;
            }

            showNotification(`找到 ${parseResponse.interfaces.length} 个接口，开始生成测试代码...`, 'info');

            // 创建测试代码生成任务
            $.post(`/aitest/api-projects/api/${projectId}/generate-tests`, function(response) {
                if (response.success) {
                    showNotification('测试代码生成任务已创建', 'success');

                    // 跳转到任务管理页面查看进度
                    window.open(`/aitest/tasks/?task_id=${response.task_id}`, '_blank');
                } else {
                    showNotification('创建测试代码生成任务失败: ' + response.message, 'error');
                }
            }).fail(function() {
                showNotification('创建测试代码生成任务失败', 'error');
            });

        } else {
            showNotification('解析接口文档失败: ' + parseResponse.message, 'error');
        }
    }).fail(function() {
        showNotification('解析接口文档失败', 'error');
    });
}

// 获取状态徽章
function getStatusBadge(status) {
    const statusMap = {
        '待开发': 'bg-secondary',
        '开发中': 'bg-primary',
        '测试中': 'bg-warning',
        '已完成': 'bg-success',
        '已暂停': 'bg-danger'
    };

    const badgeClass = statusMap[status] || 'bg-secondary';
    return `<span class="badge ${badgeClass}">${status || '待开发'}</span>`;
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 格式化日期
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
{% endblock %}
