#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import uuid
import shutil
import zipfile
import asyncio
from datetime import datetime
from typing import Dict, List, Optional
from werkzeug.utils import secure_filename
from .api_document_parser import ApiDocumentParser
from .api_test_generator import ApiTestGenerator
from .storage import StorageManager
from .utils.file_converter import FileConverter
from .utils.claude_agent import ClaudeAgent

class ApiProjectManager:
    """接口项目管理器"""
    
    def __init__(self, storage:StorageManager, config):
        self.storage = storage
        self.config = config
        self.code_path = os.path.join(config.DATA_PATH, 'code')
        self.allowed_extensions = {'doc', 'docx', 'pdf'}

        # 初始化解析器和生成器
        self.document_parser = ApiDocumentParser()
        self.test_generator = ApiTestGenerator(config)

        # 确保代码目录存在
        os.makedirs(self.code_path, exist_ok=True)
    
    def create_api_project(self, data: Dict) -> str:
        """创建接口项目"""
        project_id = str(uuid.uuid4())
        
        # 设置默认值
        data['id'] = project_id
        data['created_at'] = datetime.now().isoformat()
        data['updated_at'] = datetime.now().isoformat()
        data['language'] = data.get('language', 'Java')
        data['status'] = data.get('status', '待开发')
        
        # 创建项目目录
        project_dir = os.path.join(self.code_path, project_id)
        os.makedirs(project_dir, exist_ok=True)
        
        # 保存项目信息
        self.storage.create('api_projects', data)
        
        return project_id
    
    def get_api_project(self, project_id: str) -> Optional[Dict]:
        """获取接口项目详情"""
        return self.storage.read('api_projects', project_id)
    
    def update_api_project(self, project_id: str, data: Dict) -> bool:
        """更新接口项目"""
        data['updated_at'] = datetime.now().isoformat()
        return self.storage.update('api_projects', project_id, data)
    
    def delete_api_project(self, project_id: str) -> bool:
        """删除接口项目"""
        try:
            # 删除项目目录
            project_dir = os.path.join(self.code_path, project_id)
            if os.path.exists(project_dir):
                shutil.rmtree(project_dir)
            
            # 删除数据库记录
            return self.storage.delete('api_projects', project_id)
        except Exception as e:
            print(f"删除接口项目失败: {e}")
            return False
    
    def list_api_projects(self, page: int = 1, page_size: int = 20, 
                         search: str = None) -> Dict:
        """获取接口项目列表"""
        if search:
            # 搜索项目
            projects = self.storage.search('api_projects', search, 
                                         fields=['name', 'description'])
            
            # 分页
            total = len(projects)
            start = (page - 1) * page_size
            end = start + page_size
            page_projects = projects[start:end]
            
            return {
                'records': page_projects,
                'total': total,
                'page': page,
                'page_size': page_size,
                'total_pages': (total + page_size - 1) // page_size
            }
        else:
            return self.storage.list('api_projects', page=page, page_size=page_size)
    
    def upload_api_document(self, project_id: str, file, filename: str = None) -> Dict:
        """上传接口文档"""
        try:
            # 验证文件类型
            if not self._allowed_file(file.filename):
                return {
                    'success': False,
                    'message': '不支持的文件类型，仅支持 doc, docx, pdf 格式'
                }
            
            # 生成安全的文件名
            if filename:
                filename = secure_filename(filename)
            else:
                filename = secure_filename(file.filename)
            
            # 确保项目目录存在
            project_dir = os.path.join(self.code_path, project_id)
            os.makedirs(project_dir, exist_ok=True)
            
            # 保存文件
            file_path = os.path.join(project_dir, filename)
            file.save(file_path)
            
            # 更新项目信息
            project = self.get_api_project(project_id)
            if project:
                project['document_path'] = file_path
                project['document_name'] = filename
                project['updated_at'] = datetime.now().isoformat()
                self.update_api_project(project_id, project)
            
            return {
                'success': True,
                'message': '文档上传成功',
                'file_path': file_path,
                'filename': filename
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'文档上传失败: {str(e)}'
            }
    
    def _allowed_file(self, filename: str) -> bool:
        """检查文件类型是否允许"""
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in self.allowed_extensions
    
    def get_project_directory(self, project_id: str) -> str:
        """获取项目目录路径"""
        return os.path.join(self.code_path, project_id)
    
    def create_zip_archive(self, project_id: str) -> Optional[str]:
        """创建项目代码的zip压缩包"""
        try:
            project_dir = os.path.join(self.code_path, project_id)
            if not os.path.exists(project_dir):
                return None
            
            # 创建临时zip文件
            zip_filename = f"{project_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip"
            zip_path = os.path.join(self.code_path, zip_filename)
            
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(project_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        # 计算相对路径
                        arcname = os.path.relpath(file_path, project_dir)
                        zipf.write(file_path, arcname)
            
            return zip_path
            
        except Exception as e:
            print(f"创建zip压缩包失败: {e}")
            return None
    
    def save_generated_code(self, project_id: str, interface_name: str, 
                           code_content: str, file_extension: str = '.java') -> str:
        """保存生成的测试代码"""
        try:
            project_dir = os.path.join(self.code_path, project_id)
            os.makedirs(project_dir, exist_ok=True)
            
            # 生成文件名
            safe_name = secure_filename(interface_name)
            filename = f"{safe_name}Test{file_extension}"
            file_path = os.path.join(project_dir, filename)
            
            # 保存代码
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(code_content)
            
            return file_path
            
        except Exception as e:
            print(f"保存生成的代码失败: {e}")
            return None
    
    def get_project_files(self, project_id: str, suffix:List = []) -> List[Dict]:
        """获取项目文件列表"""
        try:
            project_dir = os.path.join(self.code_path, project_id)
            if not os.path.exists(project_dir):
                return []
            
            files = []
            # 过滤suffix的后缀
            if suffix:
                proj_files = [f for f in os.listdir(project_dir) if f.endswith(tuple(suffix))]
            else:
                proj_files = os.listdir(project_dir)
            for filename in proj_files:
                file_path = os.path.join(project_dir, filename)
                if os.path.isfile(file_path):
                    stat = os.stat(file_path)
                    files.append({
                        'name': filename,
                        'path': file_path,
                        'size': stat.st_size,
                        'modified_at': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })
            
            return files
            
        except Exception as e:
            print(f"获取项目文件列表失败: {e}")
            return []
    
    def get_project_statistics(self, project_id: str) -> Dict:
        """获取项目统计信息"""
        try:
            project_dir = os.path.join(self.code_path, project_id)
            if not os.path.exists(project_dir):
                return {
                    'total_files': 0,
                    'total_size': 0,
                    'test_files': 0,
                    'document_files': 0
                }
            
            total_files = 0
            total_size = 0
            test_files = 0
            document_files = 0
            
            for filename in os.listdir(project_dir):
                file_path = os.path.join(project_dir, filename)
                if os.path.isfile(file_path):
                    total_files += 1
                    total_size += os.path.getsize(file_path)
                    
                    if filename.endswith('Test.java') or filename.endswith('Test.py'):
                        test_files += 1
                    elif filename.endswith(('.doc', '.docx', '.pdf')):
                        document_files += 1
            
            return {
                'total_files': total_files,
                'total_size': total_size,
                'test_files': test_files,
                'document_files': document_files
            }
            
        except Exception as e:
            print(f"获取项目统计信息失败: {e}")
            return {
                'total_files': 0,
                'total_size': 0,
                'test_files': 0,
                'document_files': 0
            }

    def parse_api_document(self, project_id: str) -> Dict:
        """解析项目的接口文档"""
        try:
            project = self.get_api_project(project_id) 
            files = self.get_project_files(project_id,['.doc', '.docx', '.pdf'])
            if not files:
                return {
                    'success': False,
                    'message': '接口文档文件不存在',
                    'interfaces': []
                }

            all_results = {
                'success': True,
                'message': '接口文档解析成功',
                'interfaces': []
            }
            for document_path in files:
                # 解析文档
                result = self.document_parser.parse_document(document_path['path'])
                if not result['success']:
                    return result
                all_results['interfaces'].extend(result['interfaces'])

            # 保存解析结果到项目信息中
            project['parsed_interfaces'] = all_results['interfaces']
            project['parse_time'] = datetime.now().isoformat()
            self.update_api_project(project_id, project)

            return all_results

        except Exception as e:
            return {
                'success': False,
                'message': f'解析接口文档失败: {str(e)}',
                'interfaces': []
            }

    def generate_test_codes(self, project_id: str, progress_callback=None) -> Dict:
        """生成项目的测试代码"""
        try:
            project = self.get_api_project(project_id)
            if not project:
                return {
                    'success': False,
                    'errors': '项目不存在'
                }

            # 获取解析的接口信息
            interfaces = project.get('parsed_interfaces')
            if not interfaces:
                # 尝试重新解析文档
                parse_result = self.parse_api_document(project_id)
                if not parse_result['success']:
                    return {
                        'success': False,
                        'errors': '没有可用的接口信息，请先上传并解析接口文档'
                    }
                interfaces = parse_result['interfaces']

            if not interfaces:
                return {
                    'success': False,
                    'errors': '没有找到接口信息'
                }

            # 生成测试代码
            language = project.get('language', 'Java')
            result = self.test_generator.generate_test_code_batch(
                interfaces,
                language,
                progress_callback
            )

            if result['success']:
                # 保存生成的测试代码到文件
                saved_files = []
                for test in result['generated_tests']:
                    file_path = self.save_generated_code(
                        project_id,
                        test['interface_name'],
                        test['test_code'],
                        test['file_extension']
                    )
                    if file_path:
                        saved_files.append(file_path)

                # # 生成测试套件总结
                # summary = self.test_generator.generate_test_suite_summary(
                #     result['generated_tests'],
                #     language
                # )

                # # 保存总结文件
                # summary_path = self.save_generated_code(
                #     project_id,
                #     'TestSuite_README',
                #     summary,
                #     '.md'
                # )
                # if summary_path:
                #     saved_files.append(summary_path)

                # 更新项目信息
                project['test_generation_time'] = datetime.now().isoformat()
                project['generated_test_count'] = len(result['generated_tests'])
                self.update_api_project(project_id, project)

                result['saved_files'] = saved_files
                #result['summary_file'] = summary_path

            return result

        except Exception as e:
            return {
                'success': False,
                'errors': f'生成测试代码失败: {str(e)}'
            }
    def generate_test_codes_with_claude(self, project_id: str, progress_callback=None) -> Dict:
        """生成项目的测试代码"""
        try:
            project = self.get_api_project(project_id)
            if not project:
                return {
                    'success': False,
                    'errors': '项目不存在'
                }

            # 检查项目目录下是否有markdown文件
            # project_dir = os.path.join(self.code_path, project_id)
            # md_files = [f for f in os.listdir(project_dir) if f.endswith(".md")]
            # if not md_files:
            #     file_converter = FileConverter()
            #     api_endpoint = self.config.MINERU_API
            #     # 将当前目录下的word/pdf文档转换为markdown格式
            #     doc_files = [f for f in os.listdir(project_dir) if f.endswith(tuple(['.doc', '.docx', '.pdf']))]
            #     for doc_file in doc_files:
            #         file_converter.doc_to_markdown(os.path.join(project_dir,doc_file), api_endpoint, True)
            

            claude_agent = ClaudeAgent(self.config)
            result = asyncio.run(claude_agent.generate_test_cases(project, progress_callback))
            
            project['test_generation_time'] = datetime.now().isoformat()
            self.update_api_project(project_id, project)

            return result

        except Exception as e:
            return {
                'success': False,
                'errors': f'生成测试代码失败: {str(e)}'
            }
    def get_parsed_interfaces(self, project_id: str) -> List[Dict]:
        """获取项目解析的接口列表"""
        try:
            project = self.get_api_project(project_id)
            if project and project.get('parsed_interfaces'):
                return project['parsed_interfaces']
            return []
        except Exception as e:
            print(f"获取解析的接口列表失败: {e}")
            return []

    def update_interface_info(self, project_id: str, interface_index: int, interface_data: Dict) -> bool:
        """更新接口信息"""
        try:
            project = self.get_api_project(project_id)
            if not project or not project.get('parsed_interfaces'):
                return False

            interfaces = project['parsed_interfaces']
            if 0 <= interface_index < len(interfaces):
                interfaces[interface_index].update(interface_data)
                project['updated_at'] = datetime.now().isoformat()
                return self.update_api_project(project_id, project)

            return False
        except Exception as e:
            print(f"更新接口信息失败: {e}")
            return False
