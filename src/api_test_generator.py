#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import threading
from typing import Dict, List, Optional, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed
from .utils.llm_client import LLMClient

class ApiTestGenerator:
    """接口测试代码生成器"""
    
    def __init__(self, config):
        self.config = config
        self.llm_client = LLMClient(config)
        self.lock = threading.Lock()
    
    def generate_test_code_batch(self, interfaces: List[Dict], language: str = 'Java',
                                progress_callback: Optional[Callable] = None) -> Dict:
        """批量生成接口测试代码"""
        try:
            results = {
                'success': True,
                'message': '',                
                'total': len(interfaces),
                'completed': 0,
                'errors': ''
            }
            
            # 使用线程池并发生成
            max_workers = min(4, len(interfaces))  # 最少4个线程
            success_count = 0
            failed_count = 0
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 提交所有任务
                future_to_interface = {
                    executor.submit(self._generate_single_test, interface, language): interface
                    for interface in interfaces
                }
                
                # 处理完成的任务
                for future in as_completed(future_to_interface):
                    interface = future_to_interface[future]
                    
                    try:
                        test_result = future.result()

                        with self.lock:
                            if test_result['success']:
                                results['generated_tests'].append(test_result)
                                success_count += 1
                            else:
                                results['errors'] += f'接口 {interface.get("name", "未知")} 生成测试代码时发生错误：{test_result["message"]}\n'
                                failed_count += 1

                            results['completed'] += 1

                            # 调用进度回调
                            if progress_callback:
                                progress = (results['completed'] / results['total']) * 100
                                progress_callback(int(progress))

                    except Exception as e:
                        with self.lock:
                            failed_count += 1
                            results['completed'] += 1
                            results['errors'] += f'接口 {interface.get("name", "未知")} 处理任务时发生错误：{str(e)}\n'
            
            # 设置最终消息
            results['message'] = f'生成完成：成功 {success_count} 个，失败 {failed_count} 个'
            
            return results
            
        except Exception as e:
            return {
                'success': False,
                'message': f'批量生成测试代码失败: {str(e)}',
                'generated_tests': [],
                'total': len(interfaces),
                'completed': 0
            }
    
    def _generate_single_test(self, interface: Dict, language: str) -> Dict:
        """生成单个接口的测试代码"""
        try:
            # 构建提示词
            system_prompt = """
            # 角色
            你是一个专业的测试代码生成器，请根据用户提供的接口信息和要求生成测试代码。
            # 输出约束
            只输出生成的测试代码，不要输出其他内容。
            """
            user_content = self._build_test_generation_prompt(interface, language)
            
            # 调用LLM生成代码
            response = self.llm_client.generate_response(system_prompt, user_content)
            
            if not response.get('success'):
                return {
                    'success': False,
                    'message': f"LLM调用失败: {response.get('message', '未知错误')}"
                }
            
            # 提取生成的代码
            generated_code = response['content']
            
            # 清理和格式化代码
            cleaned_code = self._clean_generated_code(generated_code, language)
            
            return {
                'success': True,
                'interface_name': interface['name'],
                'interface_method': interface.get('method', 'POST'),
                'interface_path': interface.get('path', '/api/unknown'),
                'test_code': cleaned_code,
                'language': language,
                'file_extension': self._get_file_extension(language)
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'生成接口 {interface.get("name", "未知")} 的测试代码失败: {str(e)}'
            }
    
    def _build_test_generation_prompt(self, interface: Dict, language: str) -> str:
        """构建测试代码生成的提示词"""
        
        # 基础模板
        base_template = """
请为以下接口生成{language}语言的单元测试代码：

接口信息：
- 接口名称: {name}
- HTTP方法: {method}
- 接口路径: {path}
- 接口描述: {description}

参数信息：
{parameters}

响应信息：
{response}

请生成完整的单元测试代码，包括：
1. 正常情况的测试用例
2. 异常情况的测试用例（如参数错误、权限不足等）
3. 边界值测试用例
4. 必要的Mock和断言

要求：
- 代码要完整可运行
- 包含详细的注释
- 遵循{language}的最佳实践
- 使用常用的测试框架
"""
        
        # 格式化参数信息
        parameters_text = ""
        if interface.get('parameters'):
            parameters_text = "\n".join([
                f"- {param.get('name', '未知')}: {param.get('type', 'string')} "
                f"({'必填' if param.get('required', True) else '可选'}) - {param.get('description', '')}"
                for param in interface['parameters']
            ])
        else:
            parameters_text = "无参数"
        
        # 格式化响应信息
        response_text = json.dumps(interface.get('response', {}), ensure_ascii=False, indent=2)
        if not response_text or response_text == "{}":
            response_text = "标准JSON响应格式"
        
        return base_template.format(
            language=language,
            name=interface.get('name', '未知接口'),
            method=interface.get('method', 'POST'),
            path=interface.get('path', '/api/unknown'),
            description=interface.get('description', '无描述'),
            parameters=parameters_text,
            response=response_text
        )
    
    def _clean_generated_code(self, code: str, language: str) -> str:
        """清理和格式化生成的代码"""
        # 移除可能的markdown标记
        code = code.replace('```java', '').replace('```python', '').replace('```javascript', '')
        code = code.replace('```', '')
        
        # 移除多余的空行
        lines = code.split('\n')
        cleaned_lines = []
        prev_empty = False
        
        for line in lines:
            is_empty = not line.strip()
            if not (is_empty and prev_empty):
                cleaned_lines.append(line)
            prev_empty = is_empty
        
        return '\n'.join(cleaned_lines).strip()
    
    def _get_file_extension(self, language: str) -> str:
        """获取文件扩展名"""
        extensions = {
            'Java': '.java',
            'Python': '.py',
            'JavaScript': '.js',
            'C#': '.cs',
            'Go': '.go',
            'PHP': '.php'
        }
        return extensions.get(language, '.txt')
    
    def generate_test_suite_summary(self, generated_tests: List[Dict], language: str) -> str:
        """生成测试套件总结"""
        if not generated_tests:
            return "没有生成任何测试代码"
        
        summary_lines = [
            f"# {language} 接口测试套件",
            "",
            f"本测试套件包含 {len(generated_tests)} 个接口的单元测试代码。",
            "",
            "## 测试覆盖的接口：",
            ""
        ]
        
        for i, test in enumerate(generated_tests, 1):
            summary_lines.append(
                f"{i}. {test['interface_name']} ({test['interface_method']} {test['interface_path']})"
            )
        
        summary_lines.extend([
            "",
            "## 使用说明：",
            "",
            "1. 将生成的测试文件导入到你的测试项目中",
            "2. 根据实际情况调整测试数据和断言",
            "3. 配置测试环境和依赖",
            "4. 运行测试验证接口功能",
            "",
            "## 注意事项：",
            "",
            "- 生成的测试代码仅供参考，请根据实际业务逻辑进行调整",
            "- 确保测试环境的配置正确",
            "- 建议添加更多的边界条件和异常场景测试",
            ""
        ])
        
        return "\n".join(summary_lines)
    
    def validate_generated_code(self, code: str, language: str) -> Dict:
        """验证生成的代码"""
        validation_result = {
            'valid': True,
            'issues': [],
            'suggestions': []
        }
        
        # 基本验证
        if not code.strip():
            validation_result['valid'] = False
            validation_result['issues'].append('生成的代码为空')
            return validation_result
        
        # 语言特定验证
        if language == 'Java':
            if 'class' not in code and 'public' not in code:
                validation_result['issues'].append('Java代码缺少类定义')
            if '@Test' not in code and 'test' not in code.lower():
                validation_result['issues'].append('缺少测试方法标识')
        
        elif language == 'Python':
            if 'def test_' not in code and 'class Test' not in code:
                validation_result['issues'].append('Python代码缺少测试方法或测试类')
            if 'import' not in code:
                validation_result['suggestions'].append('建议添加必要的import语句')
        
        elif language == 'JavaScript':
            if 'describe(' not in code and 'it(' not in code and 'test(' not in code:
                validation_result['issues'].append('JavaScript代码缺少测试框架结构')
        
        # 通用验证
        if len(code.split('\n')) < 10:
            validation_result['suggestions'].append('生成的代码行数较少，建议增加更多测试场景')
        
        if validation_result['issues']:
            validation_result['valid'] = False
        
        return validation_result
