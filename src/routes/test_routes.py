#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, request, jsonify, render_template, session
import json
from ..test_manager import TestManager
from ..async_task_manager import get_task_manager

def create_test_blueprint(test_manager:TestManager):
    """创建测试路由蓝图"""
    bp = Blueprint('tests', __name__, url_prefix='/aitest/tests/')
    
    @bp.route('/usercase/')
    def index():
        """测试管理页面"""
        return render_template('tests/index.html')

    @bp.route('/automation/')
    def automation():
        """自动化测试用例页面"""
        return render_template('tests/automation.html')

    @bp.route('/automation/<case_id>')
    def automation_detail(case_id):
        """自动化测试用例详情页面"""
        return render_template('tests/automation_detail.html', case_id=case_id)
    
    @bp.route('/api/list')
    def list_test_cases():
        """获取测试用例列表API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400

        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        search = request.args.get('search', '').strip()

        # 过滤条件
        filters = {}
        if request.args.get('type'):
            filters['type'] = request.args.get('type')
        if request.args.get('status'):
            filters['status'] = request.args.get('status')
        if request.args.get('priority'):
            filters['priority'] = request.args.get('priority')

        try:
            result = test_manager.list_test_cases(
                project_id=project_id,
                page=page,
                page_size=page_size,
                filters=filters if filters else None,
                search=search if search else None
            )
            return jsonify({
                'success': True,
                'data': result
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取测试用例列表失败: {str(e)}'
            }), 500

    @bp.route('/api/automation/list')
    def list_automation_test_cases():
        """获取自动化测试用例列表API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400

        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        search = request.args.get('search', '').strip()

        # 过滤条件
        filters = {}
        if request.args.get('status'):
            filters['status'] = request.args.get('status')
        if request.args.get('framework'):
            filters['framework'] = request.args.get('framework')

        try:
            result = test_manager.list_automation_test_cases(
                project_id=project_id,
                page=page,
                page_size=page_size,
                filters=filters if filters else None,
                search=search if search else None
            )
            return jsonify({
                'success': True,
                'data': result
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取自动化测试用例列表失败: {str(e)}'
            }), 500
    
    @bp.route('/api/generate', methods=['POST'])
    def generate_test_cases_from_requirements():
        """根据需求生成测试用例API（异步）"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400

        data = request.get_json()
        requirement_ids = data.get('requirement_ids', [])
        test_types = data.get('test_types', ['functional'])
        use_knowledge_base = data.get('use_knowledge_base', True)
        max_threads = data.get('max_threads', 0)

        if not requirement_ids:
            return jsonify({
                'success': False,
                'message': '请选择需求功能点'
            }), 400

        if not test_types:
            return jsonify({
                'success': False,
                'message': '请选择测试类型'
            }), 400

        try:
            # 提交异步任务
            task_manager = get_task_manager()
            task_id = task_manager.submit_task(
                task_type='generate_test_cases',
                task_name=f'生成测试用例 - {len(requirement_ids)}个需求',
                func=test_manager.generate_test_cases_from_requirement_ids,
                args=(project_id, requirement_ids, test_types, use_knowledge_base, max_threads)
            )

            return jsonify({
                'success': True,
                'task_id': task_id,
                'message': '测试用例生成任务已启动，请稍候查看结果'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'启动生成测试用例任务失败: {str(e)}'
            }), 500

    @bp.route('/api/list_by_requirement')
    def list_test_cases_by_requirement():
        """根据功能点ID获取测试用例列表API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400

        requirement_id = request.args.get('requirement_id')
        if not requirement_id:
            return jsonify({
                'success': False,
                'message': '请提供功能点ID'
            }), 400

        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))

        try:
            # 查询与该功能点关联的测试用例
            result = test_manager.list_test_cases_by_requirement(
                project_id=project_id,
                requirement_id=requirement_id,
                page=page,
                page_size=page_size
            )
            return jsonify({
                'success': True,
                'data': result
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取测试用例列表失败: {str(e)}'
            }), 500

    @bp.route('/api/generate_automation', methods=['POST'])
    def generate_automation_test_cases():
        """生成自动化测试用例API（异步）"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400

        data = request.get_json()
        user_test_case_ids = data.get('user_test_case_ids', [])
        max_threads = data.get('max_threads', 0)

        if not user_test_case_ids:
            return jsonify({
                'success': False,
                'message': '请选择用户测试用例'
            }), 400

        try:
            # 提交异步任务
            task_manager = get_task_manager()
            task_id = task_manager.submit_task(
                task_type='generate_automation_test_cases',
                task_name=f'生成自动化测试用例 - {len(user_test_case_ids)}个用例',
                func=test_manager.generate_automation_test_cases,
                args=(project_id, user_test_case_ids, max_threads)
            )

            return jsonify({
                'success': True,
                'task_id': task_id,
                'message': '自动化测试用例生成任务已启动，请稍候查看结果'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'启动生成自动化测试用例任务失败: {str(e)}'
            }), 500
    
    @bp.route('/api/save_generated', methods=['POST'])
    def save_generated_test_cases():
        """保存生成的测试用例API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        data = request.get_json()
        test_cases = data.get('test_cases', [])
        
        if not test_cases:
            return jsonify({
                'success': False,
                'message': '没有测试用例数据'
            }), 400
        
        try:
            saved_ids = test_manager.save_generated_test_cases(test_cases)
            
            return jsonify({
                'success': True,
                'data': {'saved_ids': saved_ids},
                'message': f'成功保存 {len(saved_ids)} 个测试用例'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'保存测试用例失败: {str(e)}'
            }), 500
    
    @bp.route('/api/create', methods=['POST'])
    def create_test_case():
        """创建测试用例API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        data = request.get_json()
        
        # 验证必填字段
        if not data.get('name'):
            return jsonify({
                'success': False,
                'message': '测试用例名称不能为空'
            }), 400
        
        try:
            test_case_id = test_manager.create_test_case(project_id, data)
            
            return jsonify({
                'success': True,
                'data': {'id': test_case_id},
                'message': '测试用例创建成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'创建测试用例失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<test_case_id>')
    def get_test_case(test_case_id):
        """获取测试用例详情API"""
        project_id = session.get('current_project_id')
        
        try:
            test_case = test_manager.get_test_case(test_case_id, project_id)
            if not test_case:
                return jsonify({
                    'success': False,
                    'message': '测试用例不存在'
                }), 404
            
            return jsonify({
                'success': True,
                'data': test_case
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取测试用例详情失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<test_case_id>/update', methods=['PUT'])
    def update_test_case(test_case_id):
        """更新测试用例API"""
        project_id = session.get('current_project_id')
        data = request.get_json()
        
        try:
            success = test_manager.update_test_case(test_case_id, data, project_id)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '测试用例更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '测试用例更新失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新测试用例失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<test_case_id>/delete', methods=['DELETE'])
    def delete_test_case(test_case_id):
        """删除测试用例API"""
        project_id = session.get('current_project_id')
        
        try:
            success = test_manager.delete_test_case(test_case_id, project_id)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '测试用例删除成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '测试用例删除失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'删除测试用例失败: {str(e)}'
            }), 500
    
    @bp.route('/api/batch_delete', methods=['POST'])
    def batch_delete_test_cases():
        """批量删除测试用例API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400
        
        data = request.get_json()
        test_case_ids = data.get('test_case_ids', [])
        
        if not test_case_ids:
            return jsonify({
                'success': False,
                'message': '请选择要删除的测试用例'
            }), 400
        
        try:
            result = test_manager.batch_delete_test_cases(test_case_ids, project_id)
            
            return jsonify({
                'success': True,
                'data': result,
                'message': f'成功删除 {result["success_count"]} 个测试用例'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'批量删除失败: {str(e)}'
            }), 500
    
    @bp.route('/api/types')
    def get_test_case_types():
        """获取测试用例类型列表API"""
        try:
            types = test_manager.get_test_case_types()
            return jsonify({
                'success': True,
                'data': types
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取测试用例类型失败: {str(e)}'
            }), 500
    
    @bp.route('/api/statuses')
    def get_test_case_statuses():
        """获取测试用例状态列表API"""
        try:
            statuses = test_manager.get_test_case_statuses()
            return jsonify({
                'success': True,
                'data': statuses
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取测试用例状态失败: {str(e)}'
            }), 500
    
    @bp.route('/api/priorities')
    def get_test_case_priorities():
        """获取测试用例优先级列表API"""
        try:
            priorities = test_manager.get_test_case_priorities()
            return jsonify({
                'success': True,
                'data': priorities
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取测试用例优先级失败: {str(e)}'
            }), 500
    
    @bp.route('/api/statistics')
    def get_test_case_statistics():
        """获取测试用例统计信息API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400

        try:
            stats = test_manager.get_test_case_statistics(project_id)
            return jsonify({
                'success': True,
                'data': stats
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取测试用例统计失败: {str(e)}'
            }), 500

    # 自动化测试用例相关路由
    @bp.route('/api/automation/create', methods=['POST'])
    def create_automation_test_case():
        """创建自动化测试用例API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400

        data = request.get_json()

        # 验证必填字段
        if not data.get('name'):
            return jsonify({
                'success': False,
                'message': '自动化测试用例名称不能为空'
            }), 400

        try:
            case_id = test_manager.create_automation_test_case(project_id, data)

            return jsonify({
                'success': True,
                'data': {'id': case_id},
                'message': '自动化测试用例创建成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'创建自动化测试用例失败: {str(e)}'
            }), 500

    @bp.route('/api/automation/<case_id>')
    def get_automation_test_case(case_id):
        """获取自动化测试用例详情API"""
        project_id = session.get('current_project_id')

        try:
            automation_case = test_manager.get_automation_test_case(case_id, project_id)
            if not automation_case:
                return jsonify({
                    'success': False,
                    'message': '自动化测试用例不存在'
                }), 404

            return jsonify({
                'success': True,
                'data': automation_case
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取自动化测试用例详情失败: {str(e)}'
            }), 500

    @bp.route('/api/automation/<case_id>/update', methods=['PUT'])
    def update_automation_test_case(case_id):
        """更新自动化测试用例API"""
        project_id = session.get('current_project_id')
        data = request.get_json()

        try:
            success = test_manager.update_automation_test_case(case_id, data, project_id)

            if success:
                return jsonify({
                    'success': True,
                    'message': '自动化测试用例更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '自动化测试用例更新失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新自动化测试用例失败: {str(e)}'
            }), 500

    @bp.route('/api/automation/<case_id>/delete', methods=['DELETE'])
    def delete_automation_test_case(case_id):
        """删除自动化测试用例API"""
        project_id = session.get('current_project_id')

        try:
            success = test_manager.delete_automation_test_case(case_id, project_id)

            if success:
                return jsonify({
                    'success': True,
                    'message': '自动化测试用例删除成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '自动化测试用例删除失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'删除自动化测试用例失败: {str(e)}'
            }), 500

    @bp.route('/api/automation/batch_delete', methods=['POST'])
    def batch_delete_automation_test_cases():
        """批量删除自动化测试用例API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400

        data = request.get_json()
        case_ids = data.get('case_ids', [])

        if not case_ids:
            return jsonify({
                'success': False,
                'message': '请选择要删除的自动化测试用例'
            }), 400

        try:
            result = test_manager.batch_delete_automation_test_cases(case_ids, project_id)

            return jsonify({
                'success': True,
                'data': result,
                'message': f'成功删除 {result["success_count"]} 个自动化测试用例'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'批量删除失败: {str(e)}'
            }), 500

    @bp.route('/api/automation/batch_execute', methods=['POST'])
    def batch_execute_automation_test_cases():
        """批量执行自动化测试用例API"""
        project_id = session.get('current_project_id')
        if not project_id:
            return jsonify({
                'success': False,
                'message': '请先选择项目'
            }), 400

        data = request.get_json()
        case_ids = data.get('case_ids', [])

        if not case_ids:
            return jsonify({
                'success': False,
                'message': '请选择要执行的自动化测试用例'
            }), 400

        try:
            result = test_manager.batch_execute_automation_test_cases(case_ids, project_id)

            return jsonify({
                'success': True,
                'data': result,
                'message': f'成功启动 {result["success_count"]} 个自动化测试用例的执行'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'批量执行失败: {str(e)}'
            }), 500

    return bp
