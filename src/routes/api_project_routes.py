#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, request, jsonify, render_template, send_file
import os
from ..api_project_manager import ApiProjectManager
from ..async_task_manager import get_task_manager

def create_api_project_blueprint(api_project_manager: ApiProjectManager):
    """创建接口项目路由蓝图"""
    bp = Blueprint('api_projects', __name__, url_prefix='/aitest/api-projects')
    
    @bp.route('/')
    def index():
        """接口项目管理页面"""
        return render_template('api_projects/index.html')
    
    @bp.route('/api/list')
    def list_api_projects():
        """获取接口项目列表API"""
        page = int(request.args.get('page', 1))
        page_size = int(request.args.get('page_size', 20))
        search = request.args.get('search', '').strip()
        
        try:
            result = api_project_manager.list_api_projects(
                page=page,
                page_size=page_size,
                search=search if search else None
            )
            return jsonify({
                'success': True,
                'data': result
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取接口项目列表失败: {str(e)}'
            }), 500
    
    @bp.route('/api/create', methods=['POST'])
    def create_api_project():
        """创建接口项目API"""
        data = request.get_json()
        
        # 验证必填字段
        if not data.get('name'):
            return jsonify({
                'success': False,
                'message': '项目名称不能为空'
            }), 400
        
        try:
            project_id = api_project_manager.create_api_project(data)
            
            return jsonify({
                'success': True,
                'data': {'id': project_id},
                'message': '接口项目创建成功'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'创建接口项目失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>')
    def get_api_project(project_id):
        """获取接口项目详情API"""
        try:
            project = api_project_manager.get_api_project(project_id)
            if not project:
                return jsonify({
                    'success': False,
                    'message': '接口项目不存在'
                }), 404
            
            # 获取项目统计信息
            stats = api_project_manager.get_project_statistics(project_id)
            project['statistics'] = stats
            
            # 获取项目文件列表
            files = api_project_manager.get_project_files(project_id)
            project['files'] = files
            
            return jsonify({
                'success': True,
                'data': project
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取接口项目详情失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>/update', methods=['PUT'])
    def update_api_project(project_id):
        """更新接口项目API"""
        data = request.get_json()
        
        try:
            success = api_project_manager.update_api_project(project_id, data)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '接口项目更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '接口项目更新失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新接口项目失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>/delete', methods=['DELETE'])
    def delete_api_project(project_id):
        """删除接口项目API"""
        try:
            success = api_project_manager.delete_api_project(project_id)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '接口项目删除成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '接口项目删除失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'删除接口项目失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>/upload', methods=['POST'])
    def upload_document(project_id):
        """上传接口文档API"""
        try:
            if 'file' not in request.files:
                return jsonify({
                    'success': False,
                    'message': '没有选择文件'
                }), 400
            
            file = request.files['file']
            if file.filename == '':
                return jsonify({
                    'success': False,
                    'message': '没有选择文件'
                }), 400
            
            # 获取自定义文件名
            custom_name = request.form.get('filename')
            
            result = api_project_manager.upload_api_document(project_id, file, custom_name)
            
            if result['success']:
                # 如果没有项目名称，使用文档名称作为项目名称
                project = api_project_manager.get_api_project(project_id)
                if project and not project.get('name'):
                    filename_without_ext = os.path.splitext(result['filename'])[0]
                    api_project_manager.update_api_project(project_id, {
                        'name': filename_without_ext
                    })
                
                return jsonify(result)
            else:
                return jsonify(result), 400
                
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'上传文档失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>/download')
    def download_project(project_id):
        """下载项目代码API"""
        try:
            zip_path = api_project_manager.create_zip_archive(project_id)
            if not zip_path:
                return jsonify({
                    'success': False,
                    'message': '项目不存在或创建压缩包失败'
                }), 404
            
            # 获取项目信息用于文件名
            project = api_project_manager.get_api_project(project_id)
            download_name = f"{project.get('name', project_id)}.zip" if project else f"{project_id}.zip"
            
            return send_file(
                zip_path,
                as_attachment=True,
                download_name=download_name,
                mimetype='application/zip'
            )
            
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'下载项目失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>/files')
    def get_project_files(project_id):
        """获取项目文件列表API"""
        try:
            files = api_project_manager.get_project_files(project_id)
            return jsonify({
                'success': True,
                'data': files
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取项目文件列表失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>/statistics')
    def get_project_statistics(project_id):
        """获取项目统计信息API"""
        try:
            stats = api_project_manager.get_project_statistics(project_id)
            return jsonify({
                'success': True,
                'data': stats
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取项目统计信息失败: {str(e)}'
            }), 500
    
    @bp.route('/api/<project_id>/parse', methods=['POST'])
    def parse_api_document(project_id):
        """解析接口文档API"""
        try:
            result = api_project_manager.parse_api_document(project_id)
            return jsonify(result)
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'解析接口文档失败: {str(e)}'
            }), 500

    @bp.route('/api/<project_id>/interfaces')
    def get_parsed_interfaces(project_id):
        """获取解析的接口列表API"""
        try:
            interfaces = api_project_manager.get_parsed_interfaces(project_id)
            return jsonify({
                'success': True,
                'data': interfaces
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取接口列表失败: {str(e)}'
            }), 500

    @bp.route('/api/<project_id>/generate-tests', methods=['POST'])
    def generate_test_codes(project_id):
        """生成测试代码API（异步）"""
        try:
            # 获取任务管理器
            task_manager = get_task_manager()

            # 获取项目信息
            project = api_project_manager.get_api_project(project_id)
            if not project:
                return jsonify({
                    'success': False,
                    'message': '项目不存在'
                }), 404

            # 创建异步任务
            task_id = task_manager.submit_task(
                task_type='generate_api_tests',
                task_name=f'生成接口测试代码 - {project.get("name", "未知项目")}',
                func=api_project_manager.generate_test_codes,
                kwargs={'project_id': project_id}
            )

            return jsonify({
                'success': True,
                'task_id': task_id,
                'message': '测试代码生成任务已创建，请使用task_id查询进度和结果'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'创建测试代码生成任务失败: {str(e)}'
            }), 500

    @bp.route('/api/<project_id>/interfaces/<int:interface_index>', methods=['PUT'])
    def update_interface_info(project_id, interface_index):
        """更新接口信息API"""
        try:
            data = request.get_json()
            success = api_project_manager.update_interface_info(project_id, interface_index, data)

            if success:
                return jsonify({
                    'success': True,
                    'message': '接口信息更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '接口信息更新失败'
                }), 400

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新接口信息失败: {str(e)}'
            }), 500

    return bp
