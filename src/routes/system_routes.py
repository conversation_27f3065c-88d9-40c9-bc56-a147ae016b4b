#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from flask import Blueprint, request, jsonify, render_template
from ..system_config import SystemConfigManager

def create_system_blueprint(system_config_manager:SystemConfigManager):
    """创建系统配置路由蓝图"""
    bp = Blueprint('system', __name__, url_prefix='/aitest/system')
    
    @bp.route('/config/')
    def index():
        """系统配置页面"""
        return render_template('system/index.html')

    @bp.route('/prompts/')
    def prompts_management():
        """提示词管理页面"""
        return render_template('system/prompts.html')
    
    @bp.route('/api/llm_config')
    def get_llm_config():
        """获取大模型配置API"""
        try:
            config = system_config_manager.get_llm_config()
            # 隐藏API密钥的部分内容
            if config['api_key']:
                config['api_key'] = config['api_key'][:8] + '*' * (len(config['api_key']) - 8)
            
            return jsonify({
                'success': True,
                'data': config
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取大模型配置失败: {str(e)}'
            }), 500
    
    @bp.route('/api/llm_config', methods=['PUT'])
    def update_llm_config():
        """更新大模型配置API"""
        data = request.get_json()
        
        api_base = data.get('api_base', '').strip()
        api_key = data.get('api_key', '').strip()
        model = data.get('model', '').strip()
        
        if not all([api_base, api_key, model]):
            return jsonify({
                'success': False,
                'message': '请填写完整的配置信息'
            }), 400
        
        try:
            success = system_config_manager.update_llm_config(api_base, api_key, model)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '大模型配置更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '大模型配置更新失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新大模型配置失败: {str(e)}'
            }), 500
    
    @bp.route('/api/embedding_config')
    def get_embedding_config():
        """获取嵌入模型配置API"""
        try:
            config = system_config_manager.get_embedding_config()
            # 隐藏API密钥的部分内容
            if config['api_key']:
                config['api_key'] = config['api_key'][:8] + '*' * (len(config['api_key']) - 8)
            
            return jsonify({
                'success': True,
                'data': config
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取嵌入模型配置失败: {str(e)}'
            }), 500
    
    @bp.route('/api/embedding_config', methods=['PUT'])
    def update_embedding_config():
        """更新嵌入模型配置API"""
        data = request.get_json()
        
        api_base = data.get('api_base', '').strip()
        api_key = data.get('api_key', '').strip()
        model = data.get('model', '').strip()
        
        if not all([api_base, api_key, model]):
            return jsonify({
                'success': False,
                'message': '请填写完整的配置信息'
            }), 400
        
        try:
            success = system_config_manager.update_embedding_config(api_base, api_key, model)
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '嵌入模型配置更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '嵌入模型配置更新失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新嵌入模型配置失败: {str(e)}'
            }), 500
    
    @bp.route('/api/storage_config')
    def get_storage_config():
        """获取存储配置API"""
        try:
            config = system_config_manager.get_storage_config()
            return jsonify({
                'success': True,
                'data': config
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取存储配置失败: {str(e)}'
            }), 500
    
    @bp.route('/api/storage_config', methods=['PUT'])
    def update_storage_config():
        """更新存储配置API"""
        data = request.get_json()
        
        try:
            success = system_config_manager.update_storage_config(
                storage_type=data.get('storage_type'),
                data_path=data.get('data_path'),
                upload_folder=data.get('upload_folder'),
                knowledge_base_path=data.get('knowledge_base_path'),
                chunk_size=data.get('chunk_size'),
                chunk_overlap=data.get('chunk_overlap')
            )
            
            if success:
                return jsonify({
                    'success': True,
                    'message': '存储配置更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '存储配置更新失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新存储配置失败: {str(e)}'
            }), 500
    
    @bp.route('/api/prompts')
    def list_prompt_files():
        """获取提示词文件列表API"""
        try:
            files = system_config_manager.list_prompt_files()
            return jsonify({
                'success': True,
                'data': files
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取提示词文件列表失败: {str(e)}'
            }), 500
    
    @bp.route('/api/prompts/<filename>')
    def get_prompt_content(filename):
        """获取提示词内容API"""
        try:
            content = system_config_manager.get_prompt_content(filename)
            if content is None:
                return jsonify({
                    'success': False,
                    'message': '提示词文件不存在'
                }), 404
            
            return jsonify({
                'success': True,
                'data': {'content': content}
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取提示词内容失败: {str(e)}'
            }), 500
    
    @bp.route('/api/prompts/<filename>', methods=['PUT'])
    def update_prompt_content(filename):
        """更新提示词内容API"""
        data = request.get_json()
        content = data.get('content', '')

        try:
            success = system_config_manager.update_prompt_content(filename, content)

            if success:
                return jsonify({
                    'success': True,
                    'message': '提示词更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '提示词更新失败'
                }), 400
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新提示词失败: {str(e)}'
            }), 500

    @bp.route('/api/test_case_types')
    def get_test_case_types():
        """获取测试用例类型配置API"""
        try:
            types = system_config_manager.get_test_case_types()
            return jsonify({
                'success': True,
                'data': types
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取测试用例类型失败: {str(e)}'
            }), 500
    
    @bp.route('/api/test_llm', methods=['POST'])
    def test_llm_connection():
        """测试大模型连接API"""
        try:
            result = system_config_manager.test_llm_connection()
            return jsonify({
                'success': result['success'],
                'data': result
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'测试大模型连接失败: {str(e)}'
            }), 500
    
    @bp.route('/api/test_embedding', methods=['POST'])
    def test_embedding_connection():
        """测试嵌入模型连接API"""
        try:
            result = system_config_manager.test_embedding_connection()
            return jsonify({
                'success': result['success'],
                'data': result
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'测试嵌入模型连接失败: {str(e)}'
            }), 500
    
    @bp.route('/api/system_info')
    def get_system_info():
        """获取系统信息API"""
        try:
            info = system_config_manager.get_system_info()
            return jsonify({
                'success': True,
                'data': info
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取系统信息失败: {str(e)}'
            }), 500
    
    @bp.route('/api/config')
    def get_config():
        """获取系统配置API"""
        try:
            config = system_config_manager.get_all_config()
            # 隐藏敏感信息
            if config.get('OPENAI_API_KEY'):
                config['OPENAI_API_KEY'] = config['OPENAI_API_KEY'][:8] + '*' * max(0, len(config['OPENAI_API_KEY']) - 8)
            if config.get('EMBEDDING_API_KEY'):
                config['EMBEDDING_API_KEY'] = config['EMBEDDING_API_KEY'][:8] + '*' * max(0, len(config['EMBEDDING_API_KEY']) - 8)

            return jsonify({
                'success': True,
                'data': config
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'获取系统配置失败: {str(e)}'
            }), 500

    @bp.route('/api/config', methods=['POST'])
    def update_config():
        """更新系统配置API"""
        data = request.get_json()

        try:
            # 如果API密钥是被隐藏的形式(****)，则不更新该字段
            if data.get('OPENAI_API_KEY') and data['OPENAI_API_KEY'].endswith('****'):
                # 从当前配置中获取原始的API密钥
                current_config = system_config_manager.get_all_config()
                data['OPENAI_API_KEY'] = current_config.get('OPENAI_API_KEY', '')
            
            if data.get('EMBEDDING_API_KEY') and data['EMBEDDING_API_KEY'].endswith('****'):
                # 从当前配置中获取原始的API密钥
                current_config = system_config_manager.get_all_config()
                data['EMBEDDING_API_KEY'] = current_config.get('EMBEDDING_API_KEY', '')
            success = system_config_manager.update_all_config(data)

            if success:
                return jsonify({
                    'success': True,
                    'message': '系统配置更新成功'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': '系统配置更新失败'
                }), 400

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'更新系统配置失败: {str(e)}'
            }), 500

    @bp.route('/api/test_llm', methods=['POST'])
    def test_llm():
        """测试LLM连接API"""
        try:
            import time
            start_time = time.time()

            result = system_config_manager.test_llm_connection()

            response_time = int((time.time() - start_time) * 1000)

            if result['success']:
                return jsonify({
                    'success': True,
                    'data': {
                        'response_time': response_time,
                        'message': result.get('message', 'LLM连接正常')
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'message': result.get('message', 'LLM连接失败')
                }), 400

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'测试LLM连接失败: {str(e)}'
            }), 500

    @bp.route('/api/test_embedding', methods=['POST'])
    def test_embedding():
        """测试嵌入模型连接API"""
        try:
            import time
            start_time = time.time()

            result = system_config_manager.test_embedding_connection()

            response_time = int((time.time() - start_time) * 1000)

            if result['success']:
                return jsonify({
                    'success': True,
                    'data': {
                        'response_time': response_time,
                        'message': result.get('message', '嵌入模型连接正常')
                    }
                })
            else:
                return jsonify({
                    'success': False,
                    'message': result.get('message', '嵌入模型连接失败')
                }), 400

        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'测试嵌入模型连接失败: {str(e)}'
            }), 500

    return bp
