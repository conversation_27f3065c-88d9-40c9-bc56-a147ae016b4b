#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import json
from typing import Dict, List, Optional
from docx import Document
import PyPDF2

class ApiDocumentParser:
    """接口文档解析器"""
    
    def __init__(self):
        self.interfaces = []
    
    def parse_document(self, file_path: str) -> Dict:
        """解析接口文档"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            
            if file_ext in ['.doc', '.docx']:
                return self._parse_word_document(file_path)
            elif file_ext == '.pdf':
                return self._parse_pdf_document(file_path)
            else:
                return {
                    'success': False,
                    'message': '不支持的文件格式',
                    'interfaces': []
                }
                
        except Exception as e:
            return {
                'success': False,
                'message': f'解析文档失败: {str(e)}',
                'interfaces': []
            }
    
    def _parse_word_document(self, file_path: str) -> Dict:
        """解析Word文档"""
        try:
            doc = Document(file_path)
            text_content = []
            
            # 提取所有段落文本
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_content.append(paragraph.text.strip())
            
            # 提取表格内容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_content.append(' | '.join(row_text))
            
            full_text = '\n'.join(text_content)
            interfaces = self._extract_interfaces_from_text(full_text)
            
            return {
                'success': True,
                'message': f'成功解析文档，找到 {len(interfaces)} 个接口',
                'interfaces': interfaces
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'解析Word文档失败: {str(e)}',
                'interfaces': []
            }
    
    def _parse_pdf_document(self, file_path: str) -> Dict:
        """解析PDF文档"""
        try:
            text_content = []
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text.strip():
                        text_content.append(text.strip())
            
            full_text = '\n'.join(text_content)
            interfaces = self._extract_interfaces_from_text(full_text)
            
            return {
                'success': True,
                'message': f'成功解析文档，找到 {len(interfaces)} 个接口',
                'interfaces': interfaces
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f'解析PDF文档失败: {str(e)}',
                'interfaces': []
            }
    
    def _extract_interfaces_from_text(self, text: str) -> List[Dict]:
        """从文本中提取接口信息"""
        interfaces = []
        
        # 常见的接口模式
        patterns = [
            # REST API 模式
            r'(?i)(GET|POST|PUT|DELETE|PATCH)\s+([/\w\-{}]+)',
            # 接口名称模式
            r'(?i)接口名称[:：]\s*([^\n\r]+)',
            r'(?i)API名称[:：]\s*([^\n\r]+)',
            r'(?i)方法名[:：]\s*([^\n\r]+)',
            # URL模式
            r'(?i)URL[:：]\s*([^\n\r]+)',
            r'(?i)地址[:：]\s*([^\n\r]+)',
        ]
        
        # 尝试匹配不同的模式
        for pattern in patterns:
            matches = re.finditer(pattern, text, re.MULTILINE)
            for match in matches:
                if len(match.groups()) >= 2:
                    method = match.group(1).upper()
                    path = match.group(2).strip()
                    interface_name = f"{method} {path}"
                else:
                    interface_name = match.group(1).strip()
                    method = "POST"  # 默认方法
                    path = "/api/unknown"
                
                # 尝试提取更多信息
                interface_info = self._extract_interface_details(text, interface_name, match.start())
                
                interface = {
                    'name': interface_name,
                    'method': method,
                    'path': path,
                    'description': interface_info.get('description', ''),
                    'parameters': interface_info.get('parameters', []),
                    'response': interface_info.get('response', {}),
                    'examples': interface_info.get('examples', [])
                }
                
                # 避免重复
                if not any(existing['name'] == interface['name'] for existing in interfaces):
                    interfaces.append(interface)
        
        # 如果没有找到明确的接口，尝试从文档结构中推断
        if not interfaces:
            interfaces = self._infer_interfaces_from_structure(text)
        
        return interfaces
    
    def _extract_interface_details(self, text: str, interface_name: str, start_pos: int) -> Dict:
        """提取接口详细信息"""
        # 获取接口周围的文本（前后各500字符）
        context_start = max(0, start_pos - 500)
        context_end = min(len(text), start_pos + 1000)
        context = text[context_start:context_end]
        
        details = {
            'description': '',
            'parameters': [],
            'response': {},
            'examples': []
        }
        
        # 提取描述
        desc_patterns = [
            r'(?i)描述[:：]\s*([^\n\r]+)',
            r'(?i)说明[:：]\s*([^\n\r]+)',
            r'(?i)功能[:：]\s*([^\n\r]+)',
        ]
        
        for pattern in desc_patterns:
            match = re.search(pattern, context)
            if match:
                details['description'] = match.group(1).strip()
                break
        
        # 提取参数信息
        param_patterns = [
            r'(?i)参数[:：]\s*([^\n\r]+)',
            r'(?i)入参[:：]\s*([^\n\r]+)',
            r'(?i)请求参数[:：]\s*([^\n\r]+)',
        ]
        
        for pattern in param_patterns:
            matches = re.finditer(pattern, context)
            for match in matches:
                param_text = match.group(1).strip()
                # 简单解析参数（实际项目中可能需要更复杂的解析）
                if param_text:
                    details['parameters'].append({
                        'name': param_text,
                        'type': 'string',
                        'required': True,
                        'description': param_text
                    })
        
        return details
    
    def _infer_interfaces_from_structure(self, text: str) -> List[Dict]:
        """从文档结构推断接口"""
        interfaces = []
        
        # 按行分割文本
        lines = text.split('\n')
        
        current_interface = None
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检查是否是标题行（可能是接口名称）
            if self._is_likely_interface_title(line):
                if current_interface:
                    interfaces.append(current_interface)
                
                current_interface = {
                    'name': line,
                    'method': 'POST',
                    'path': f'/api/{len(interfaces) + 1}',
                    'description': line,
                    'parameters': [],
                    'response': {},
                    'examples': []
                }
            elif current_interface and self._is_likely_parameter(line):
                # 添加参数
                current_interface['parameters'].append({
                    'name': line,
                    'type': 'string',
                    'required': True,
                    'description': line
                })
        
        # 添加最后一个接口
        if current_interface:
            interfaces.append(current_interface)
        
        return interfaces
    
    def _is_likely_interface_title(self, line: str) -> bool:
        """判断是否可能是接口标题"""
        # 简单的启发式规则
        keywords = ['接口', 'API', '方法', '服务', '查询', '获取', '创建', '更新', '删除']
        return any(keyword in line for keyword in keywords) and len(line) < 100
    
    def _is_likely_parameter(self, line: str) -> bool:
        """判断是否可能是参数"""
        # 简单的启发式规则
        param_indicators = ['参数', '字段', 'param', 'field', '属性']
        return any(indicator in line.lower() for indicator in param_indicators) and len(line) < 200
    
    def split_interfaces_for_threading(self, interfaces: List[Dict], max_threads: int = 4) -> List[List[Dict]]:
        """将接口列表分割为多个线程处理的批次"""
        if not interfaces:
            return []
        
        # 计算每个线程处理的接口数量
        interfaces_per_thread = max(1, len(interfaces) // max_threads)
        
        batches = []
        for i in range(0, len(interfaces), interfaces_per_thread):
            batch = interfaces[i:i + interfaces_per_thread]
            batches.append(batch)
        
        # 确保不超过最大线程数
        if len(batches) > max_threads:
            # 将多余的批次合并到最后一个批次
            last_batch = batches[max_threads - 1]
            for i in range(max_threads, len(batches)):
                last_batch.extend(batches[i])
            batches = batches[:max_threads]
        
        return batches
