#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
from typing import List, Dict, Optional
import json
import re
import time
import threading
from ..config import Config

# 全局限流状态变量（线程安全）
_rate_limit_lock = threading.Lock()
qpm_counter = 0
tpm_counter = 0
API_QPM = 60
API_TPM = 300000
window_start_time = time.time()

class LLMClient:
    """大语言模型客户端"""
    
    def __init__(self, config:Config):
        self.config = config
    
    def chat_completion(self, messages: List[Dict]) -> str:
        """调用聊天完成API"""
        global qpm_counter, tpm_counter, window_start_time, _rate_limit_lock  # 使用全局限流变量    
        temperature = self.config.TEMPERATURE
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.config.OPENAI_API_KEY}"
        }
        stream = True
        data = {
            "model": self.config.OPENAI_MODEL,
            "messages": messages,
            "temperature": temperature,
            "stream": stream,
            "max_tokens": self.config.MAX_TOKENS
        }

        # 新增限流逻辑（线程安全）
        input_token_count = len(json.dumps(data))  # 粗略估算=字节长度

        with _rate_limit_lock:
            current_time = time.time()
            # 重置窗口
            if current_time - window_start_time >= 60:
                qpm_counter = 0
                tpm_counter = 0
                window_start_time = current_time

            # QPM/TPM等待逻辑
            while qpm_counter >= API_QPM or tpm_counter + input_token_count > API_TPM:
                sleep_time = max(60 - (current_time - window_start_time) + 0.1, 0)
                print(f"[线程{threading.current_thread().name}] 达到限流阈值，等待{sleep_time:.1f}秒...")
                _rate_limit_lock.release()  # 释放锁让其他线程可以检查
                time.sleep(sleep_time)
                _rate_limit_lock.acquire()  # 重新获取锁
                current_time = time.time()
                if current_time - window_start_time >= 60:
                    qpm_counter = 0
                    tpm_counter = 0
                    window_start_time = current_time

            # 更新计数器
            qpm_counter += 1
            tpm_counter += input_token_count
        
        endpoint_url = self.config.OPENAI_API_BASE + "/chat/completions"
        content = ""
        with requests.post(endpoint_url, headers=headers, json=data, stream=stream) as response:
            try:
                response.raise_for_status()
            except requests.exceptions.HTTPError as e:
                # 打印错误状态码和响应体
                print(f"HTTP Error {response.status_code}: {e}")
                print(f"Response Body: {response.text[:100]}")
                raise 
            for line in response.iter_lines():
                if line:
                    decoded_line = line.decode('utf-8')
                    if decoded_line.startswith("data: "):
                        try:
                            chunk = json.loads(decoded_line[6:])  # 去除"data: "前缀并解析
                            delta_content = chunk["choices"][0]["delta"].get("content", "")
                            if delta_content:                            
                                content += delta_content
                        except json.JSONDecodeError:
                            continue
        
        # 输出token估算（线程安全）
        output_token_count = len(content)
        with _rate_limit_lock:
            tpm_counter += output_token_count
        
        return content
    
    def extract_requirements(self, content: str, system_prompt: str = None) -> List[Dict]:
        """从文档内容中提取需求功能点"""
        if not system_prompt:
            system_prompt = "你是一个专业的需求分析师。"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请分析以下文档内容，提取需求功能点：\n\n{content} {self.config.NO_THINK}"}
        ]
        
        try:
            response = self.chat_completion(messages)
            #print(f"开始调用LLM提取功能点：{content[:100]}")
            
            # 尝试解析JSON响应
            requirements = self.extract_json_from_content(response)
            if isinstance(requirements, list):
                return requirements
            elif isinstance(requirements, dict) and 'requirements' in requirements:
                return requirements['requirements']
            else:
                raise ValueError(f"响应格式不正确:{self.remove_think(response)}")
            
        except Exception as e:
            raise Exception(f"需求提取失败: {str(e)}")
    
    def generate_test_cases(self, req_content: str, knowledge_context: str = "", 
                           system_prompt: str = None) -> List[Dict]:
        """根据需求功能点生成测试用例"""
        if not system_prompt:
            system_prompt = "你是一个专业的测试工程师。"
        
        user_content = f"需求功能点：\n{req_content}"
        if knowledge_context:
            user_content += f"\n\n参考知识库内容：\n{knowledge_context}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"{user_content} {self.config.NO_THINK}"}
        ]
        
        try:
            response = self.chat_completion(messages)
            
            # 尝试解析JSON响应
            test_cases = self.extract_json_from_content(response)
            if isinstance(test_cases, list):
                return test_cases
            elif isinstance(test_cases, dict) and 'test_cases' in test_cases:
                return test_cases['test_cases']
            else:
                raise ValueError("响应格式不正确")
                
        except Exception as e:
            raise Exception(f"测试用例生成失败: {str(e)}")   
            
    
    def generate_automation_test_cases(self, user_test_cases: Dict, 
                                     system_prompt: str = None, nothink="/nothink") -> str:
        """根据用户测试用例生成自动化测试用例"""
        if not system_prompt:
            system_prompt = "你是一个专业的自动化测试工程师。"        
        
        
        content = f"用户测试用例：{user_test_cases} {nothink}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": content}
        ]
        
        try:
            response = self.chat_completion(messages)
            
            # 响应是YAML格式
            yaml_script = self.extract_yaml_from_content(response)
            return yaml_script
                
        except Exception as e:
            raise Exception(f"自动化测试用例生成失败: {str(e)}")
    def generate_response(self, system_prompt: str, user_content: str, 
                                     nothink="/nothink") -> str:        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_content + f" {nothink}"}
        ]
        result = {
            'success': True,
            'content': '',
            'message': ''
        }
        try:
            response = self.chat_completion(messages)
            result["content"] = self.extract_code_from_content(response, 'java')
        except Exception as e:
            result['success'] = False
            result['message'] =(f"响应失败: {str(e)}")
        
        return result
    
    def extract_json_from_content(self, content):
        """
        从大模型返回的 content 字符串中提取并解析 JSON 代码块
        """
        # 用正则表达式提取 ```json ... ``` 之间的内容
        # print(f"解析json: {content}")
        match = re.search(r"``json\s*(.*?)\s*```", content, re.DOTALL)
        if match:
            json_str = match.group(1)
            try:
                # 解析为 Python 对象
                return json.loads(json_str)
            except json.JSONDecodeError as e:
                print("JSON解析失败：", e)
                return None
        else:
            try:
                if '</think>' in content:
                    content = content.split('</think>')[-1]
                content = content.replace('```json','').replace('```json','')
                content= content.strip()
                content_json = json.loads(content)
                return content_json
            except:
                pass
            print(f"未找到JsoN代码块：{content[:100]}({len(content)})")
            return None

    def extract_code_from_content(self, content, language: str = 'java'):
        """
        从大模型返回的 content 字符串中提取并解析 java 代码块
        """
        # 用正则表达式提取 ```java ... ``` 之间的内容
        match = re.search(r"``java\s*(.*?)\s*```", content, re.DOTALL)
        if match:
            java_str = match.group(1)
            return java_str
        else:
            try:
                if '</think>' in content:
                    content = content.split('</think>')[-1]
                content = content.replace('```java','').replace('```json','')
                content= content.strip()
                content_json = json.loads(content)
                return content_json
            except:
                pass
            print(f"未找到java代码块：{content[:100]}({len(content)})")
            return None
    def remove_think(self,  text):
            """
            移除字符串中所有<ai>和</ai>标签及其之间的内容。

            参数:
                text (str): 输入的字符串

            返回:
                str: 处理后的字符串
            """
            pattern = r'<think>.*?</think>'
            while True:
                new_text = re.sub(pattern, '', text, flags=re.DOTALL)
                if new_text == text:
                    break
                text = new_text
            if all(c in ('\n', ' ') for c in text):
                text = text.replace('\n', '').replace(' ', '')
            return
    def extract_yaml_from_content(self, content):
        """
        从大模型返回的 content 字符串中提取并解析 yaml 代码块
        """
        # 用正则表达式提取 ```yaml ... ``` 之间的内容
        match = re.search(r"``yaml\s*(.*?)\s*```", content, re.DOTALL)
        if match:
            yaml_str = match.group(1)
            return yaml_str
        else:
            try:
                if '</think>' in content:
                    content = content.split('</think>')[-1]
                content = content.replace('```yaml','').replace('```yaml','')
                content= content.strip()
                return content
            except:
                pass
            print(f"未找到yaml代码块：{content[:100]}({len(content)})")
            return None
