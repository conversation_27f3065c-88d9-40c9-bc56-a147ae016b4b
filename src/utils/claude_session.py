"""
Claude Shell会话管理
"""

import asyncio
import anyio
import subprocess
import signal
import os
import sys
import threading
import re
import time
import select
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
from loguru import logger
import queue
from anyio.streams.text import TextReceiveStream, TextSendStream
from claude_code_sdk import ClaudeSDKClient, ClaudeCodeOptions,ResultMessage
import pty


class ClaudeSession:
    """Claude会话管理器"""
    
    def __init__(self):
        self.process: Optional[subprocess.Popen] = None
        self.is_connected = False
        self._init_timeout = 60
        self._command_timeout = 600
        self._output_buffer = ""
        self._session_start_time = None
        self.output_queue = queue.Queue()
        self._seen_lines = set()  # 用于去重
        self._last_output_time = 0
    
    async def start_session(self, work_dir:str, session_id: str) -> bool:
        """启动Claude会话"""
        is_ready = False
        try:
            # 记录会话开始时间
            self._session_start_time = time.time()

            # 构建Claude命令
            claude_cmd = ["claude", "-r", session_id]

            logger.info(f"启动claude会话: {' '.join(claude_cmd)}")

            # 启动Claude进程
            self.master, self.slave = pty.openpty()
            import signal
            self.process = subprocess.Popen(
                claude_cmd,
                cwd=work_dir,
                stdin=self.slave,
                stdout=self.slave,
                stderr=self.slave,
                text=True,
                preexec_fn=os.setsid
            )
            os.close(self.slave)
           
            # 等待Claude启动
            start_time = time.time()
            while time.time() - start_time < 30:
                if self.process.poll() is None:
                    break
                await asyncio.sleep(0.1)  # 短暂等待，避免高CPU占用

            # 检查进程是否正常启动
            if self.process.poll() is not None:
                logger.error("Claude 会话启动失败")
                return False
            
            self._start_reader()

            # 等待并处理信任确认对话框
            await self._handle_trust_dialog()

            # 等待Claude完全准备就绪
            is_ready = await self._wait_for_ready()

            if is_ready:
                logger.info("Claude会话启动成功")
            else:
                logger.error("Claude会话启动失败")
            
        except BaseException as e:
            logger.error(f"启动Claude会话失败: {e}")
        return is_ready
    def _start_reader(self):
        def read_pty_output():
            buffer = ""
            last_read_time = time.time()

            while True:
                try:
                    # 使用非阻塞读取
                    ready, _, _ = select.select([self.master], [], [], 0.1)
                    if ready:
                        try:
                            # 读取可用的所有数据
                            data = os.read(self.master, 4096)
                            if data:
                                decoded = data.decode('utf-8', errors='ignore')
                                buffer += decoded
                                last_read_time = time.time()

                                # 按行处理缓冲区中的内容
                                while '\n' in buffer:
                                    line, buffer = buffer.split('\n', 1)
                                    line = line.strip()
                                    if line:  # 只处理非空行
                                        # 使用内容哈希进行去重
                                        line_hash = hash(line)
                                        current_time = time.time()

                                        # 如果是新内容或者距离上次输出超过5秒（避免丢失重要更新）
                                        if (line_hash not in self._seen_lines or
                                            current_time - self._last_output_time > 5):
                                            self._seen_lines.add(line_hash)
                                            self._last_output_time = current_time

                                            if "? for shortcuts" in line:
                                                print(f"CLAUDE: {line}", flush=True)
                                            self.output_queue.put(line)

                                            # 限制seen_lines的大小，避免内存泄漏
                                            if len(self._seen_lines) > 1000:
                                                self._seen_lines.clear()
                            else:
                                # 读取到0字节，表示PTY关闭
                                break
                        except OSError as e:
                            if e.errno == 11:  # EAGAIN - 没有更多数据
                                pass
                            else:
                                break
                    else:
                        # 没有数据可读，检查是否有未处理的缓冲区内容
                        current_time = time.time()
                        if buffer and (current_time - last_read_time > 0.5):
                            # 如果缓冲区有内容且超过0.5秒没有新数据，处理剩余内容
                            line = buffer.strip()
                            if line:
                                line_hash = hash(line)
                                if (line_hash not in self._seen_lines or
                                    current_time - self._last_output_time > 5):
                                    self._seen_lines.add(line_hash)
                                    self._last_output_time = current_time

                                    if "? for shortcuts" in line:
                                        print(f"CLAUDE: {line}", flush=True)
                                    self.output_queue.put(line)
                            buffer = ""

                        # 短暂休眠以避免高CPU占用
                        time.sleep(0.01)

                except Exception as e:
                    logger.error(f"PTY读取异常: {e}")
                    break

            # 处理剩余缓冲区内容
            if buffer:
                line = buffer.strip()
                if line:
                    line_hash = hash(line)
                    if line_hash not in self._seen_lines:
                        self._seen_lines.add(line_hash)
                        self.output_queue.put(line)

        t = threading.Thread(target=read_pty_output, daemon=True)
        t.start()

    async def _handle_trust_dialog(self):
        """处理Claude CLI的信任确认对话框"""
        try:
            logger.debug("等待信任确认对话框...")
            start_time = time.time()

            while time.time() - start_time < 30:  # 最多等待30秒
                if self.process.poll() is not None:
                    logger.error("Claude进程在信任确认期间退出")
                    return False

                lines = self._get_output(timeout=2)
                for line in lines:
                    if "Do you trust the files in this folder?" in line:
                        logger.debug("检测到信任确认对话框，发送确认...")
                        # 发送 "1" 选择 "Yes, proceed"
                        self._send_command("1")
                        await asyncio.sleep(1)
                        return True
                    elif "? for shortcuts" in line:
                        # 如果直接看到提示符，说明不需要信任确认
                        logger.debug("直接看到提示符，无需信任确认")
                        return True

                await asyncio.sleep(0.1)

            logger.warning("未检测到信任确认对话框或提示符")
            return False

        except Exception as e:
            logger.error(f"处理信任确认对话框失败: {e}")
            return False
    
    def _send_command(self, command):
        if self.process and self.master:
            # 通过pty的master端口发送命令，而不是stdin
            os.write(self.master, (command + '\n').encode('utf-8'))

    def _get_output(self, timeout=60) -> List[str]:
        lines = []
        start = time.time()
        while True:
            try:
                line = self.output_queue.get(timeout=timeout)
                lines.append(line)
                # 增加对 Claude 提示符的检测
                if "? for shortcuts" in line.strip():
                    break
                if time.time() - start > timeout:
                    break
            except queue.Empty:
                break
        return lines
    def _clear_output_queue(self):
        """清空输出队列中的所有消息"""
        while not self.output_queue.empty():
            try:
                self.output_queue.get_nowait()
            except queue.Empty:
                break
        # 同时清空去重缓存
        self._seen_lines.clear()
        self._last_output_time = time.time()

    def close(self):
        if self.process:
            self._send_command('quit')
            self.process.terminate()    
    async def _wait_for_ready(self) -> bool:
        """等待Claude完全准备就绪"""
        try:
            logger.debug("等待Claude准备就绪...")
            start_time = time.time()

            # 读取初始输出直到看到(Claude)提示符
            while time.time() - start_time < self._init_timeout:
                if self.process.poll() is not None:
                    logger.error("Claude进程在初始化期间退出")
                    return False
                
                lines = self._get_output(self._init_timeout)
                for line in lines:
                    #print(f" ==={line}")
                    if line and "? for shortcuts" in line.strip():
                        # 检查是否看到Claude提示符，表示准备就绪
                        logger.debug("Claude会话准备就绪")
                        self.is_connected = True
                        return True
                    # 如果看到错误信息，记录但继续等待
                    # if line.startswith("^error"):
                    #     logger.warning(f"Claude初始化时出现错误: {line}")
                # 短暂休眠
                await asyncio.sleep(0.1)

            # 超时但没有看到提示符，尝试发送一个简单命令测试
            logger.error("未在预期时间内看到Claude提示符...")

        except BaseException as e:
            logger.error(f"等待Claude准备就绪失败: {e}")
        
        return False
    
    def execute_command(self, command: str, command_timeout:int = 1) -> List[str]:
        """执行Claude命令"""
        if not self.is_connected or not self.process:
            raise RuntimeError("Claude会话未连接")
        
        try:
            start_time = time.time()
            event = (f"执行Claude命令: {command}")
            #logger.debug(f"执行Claude命令: {command}")
            # 清理之前命令的输出
            self._clear_output_queue()
            self._send_command(command)
            
            # try:
            #     await asyncio.sleep(0.1)
            # except BaseException:
            #     pass
            
            output = self._get_output(command_timeout)
            #logger.info(f"Claude-OUT: {output}")
            event = f"{event}, 输出: {output}"
            return output
        except BaseException as e:
            logger.error(f"执行Claude命令失败: {e}")
            return [""]
        
    # 读取控制台已有的输出
    async def clear_console(self, timeout: int = 5) -> str:
        try:
            start_time = time.time()
            consecutive_empty_reads = 0

            while True:
                # 超时检查
                if time.time() - start_time > timeout:
                    break

                # 进程状态检查
                if self.process.poll() is not None:
                    logger.error("Claude进程已退出")
                    break

                # 读取一行
                try:
                    line = await self._read_line()
                    if line:
                        logger.info(f"Claude输出: {line}")
                        consecutive_empty_reads = 0
                    else:
                        consecutive_empty_reads += 1
                        # 如果连续多次读取为空，且已有输出，可能命令已完成
                        if consecutive_empty_reads >= 10 :
                            logger.debug(f"连续{consecutive_empty_reads}次空读取，可能命令已完成")
                            break

                except BaseException as e:
                    logger.error(f"读取行失败: {e}")
                    consecutive_empty_reads += 1
                    continue

                # 短暂休眠，避免CPU占用过高
                await asyncio.sleep(0.01)

            return ""

        except BaseException as e:
            logger.error(f"读取Claude输出失败: {e}")
            return ""
    
    async def _read_line(self) -> str:
        """读取一行输出，带超时控制"""
        if not self.process or not self.process.stdout:
            return ""

        try:
            # 使用asyncio.wait_for添加超时控制
            loop = asyncio.get_event_loop()

            # 设置单行读取超时为1秒，避免无限期阻塞
            line = await asyncio.wait_for(
                loop.run_in_executor(None, self._readline_with_timeout),
                timeout=1.0
            )
            line = line.strip() if line else ""
            if line:
                logger.info(f"Claude-OUT: {line}")
            return line

        except BaseException as e:
            logger.error(f"读取行时发生异常: {e}")
            return ""

    def _readline_with_timeout(self) -> str:
        """带超时的readline实现"""
        if not self.process or not self.process.stdout:
            return ""

        try:
            # 检查是否有数据可读
            import select

            # 在Unix系统上使用select检查是否有数据可读
            if hasattr(select, 'select'):
                ready, _, _ = select.select([self.process.stdout], [], [], 0.1)
                if ready:
                    return self.process.stdout.readline()
                else:
                    return ""
            else:
                # Windows系统回退到直接读取
                return self.process.stdout.readline()

        except BaseException as e:
            logger.error(f"readline_with_timeout异常: {e}")
            return ""
    
    async def close_session(self):
        """关闭Claude会话"""
        try:
            if self.process:
                logger.info("关闭Claude会话")

                # 计算会话持续时间
                session_duration = 0
                if self._session_start_time:
                    session_duration = time.time() - self._session_start_time

                # 尝试优雅退出
                exit_code = None
                try:
                    await self.execute_command("/quit")
                    await asyncio.sleep(1)
                    exit_code = self.process.poll()
                except:
                    pass

                # 强制终止进程
                if self.process.poll() is None:
                    self.process.terminate()
                    await asyncio.sleep(1)

                    if self.process.poll() is None:
                        self.process.kill()
                        exit_code = -9  # SIGKILL
                    else:
                        exit_code = self.process.poll()

                self.process = None
                self.is_connected = False

                logger.info(f"Claude会话已关闭，用时:{session_duration}")

        except BaseException as e:
            logger.error(f"关闭Claude会话失败: {e}")

    def get_sessinon_id(self):
        out_lines = self.execute_command("/status")
        # 从输出中解析 L Session ID:: d103531f-eb9f-48c2-adc9-c0c43f52247e
        for line in out_lines:
            if "Session ID:" in line:
                return line.split(":")[1].strip()
        return ""

async def main():
    session = ClaudeSession()
    # work_dir = "/mnt/d/aicode/intelliTest/data/code/a96ca61c-c234-49b0-8603-c8c1cd2f9771"
    # session_id = "7533deb1-5fd7-465c-aa34-bc2e9123c0f6"
    work_dir = "/ddrive/aicode/intelliTest/data/code/a96ca61c-c234-49b0-8603-c8c1cd2f9771"
    session_id = "79e01bea-0200-4681-9a1d-c5602dfd71de"

    if await session.start_session(work_dir, session_id):
        new_session_id = session.get_sessinon_id()
        print(f"new_session_id = {new_session_id}")
        await session.close_session()
    else:
        print("启动会话失败")
    
if __name__ == "__main__":
    asyncio.run(main())