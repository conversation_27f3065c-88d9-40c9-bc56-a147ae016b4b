"""
Claude Shell会话管理
"""

import asyncio
import anyio
import subprocess
import signal
import os
import sys
import threading
import re
import time
import select
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
from loguru import logger
import queue
from anyio.streams.text import TextReceiveStream, TextSendStream
from claude_code_sdk import ClaudeSDKClient, ClaudeCodeOptions,ResultMessage
import pty
import string

class ClaudeSession:
    """Claude会话管理器"""
    
    def __init__(self):
        self.process: Optional[subprocess.Popen] = None
        self.is_connected = False
        self._init_timeout = 60
        self._command_timeout = 600
        self._output_buffer = ""
        self._session_start_time = None
        self.output_queue = queue.Queue()
    
    async def start_session(self, work_dir:str, session_id: str) -> bool:
        """启动Claude会话"""
        is_ready = False
        try:
            # 记录会话开始时间
            self._session_start_time = time.time()

            # 构建Claude命令
            claude_cmd = ["claude", "-r", session_id]

            logger.info(f"启动claude会话: {' '.join(claude_cmd)}")

            # 启动Claude进程
            self.master, self.slave = pty.openpty()
            import signal
            self.process = subprocess.Popen(
                claude_cmd,
                cwd=work_dir,
                stdin=self.slave,
                stdout=self.slave,
                stderr=self.slave,
                text=True,
                preexec_fn=os.setsid
            )
            os.close(self.slave)
           
            # 等待Claude启动
            start_time = time.time()
            while time.time() - start_time < 30:
                if self.process.poll() is None:
                    break
                await asyncio.sleep(0.1)  # 短暂等待，避免高CPU占用

            # 检查进程是否正常启动
            if self.process.poll() is not None:
                logger.error("Claude 会话启动失败")
                return False
            
            self._start_reader()
            
            # 等待Claude完全准备就绪
            is_ready = await self._wait_for_ready()
            self._clear_output_queue()
            is_ready = await self._wait_for_ready()
            self._clear_output_queue()
            is_ready = await self._wait_for_ready()
            
            await asyncio.sleep(60000)
            
        except BaseException as e:
            logger.error(f"启动Claude会话失败: {e}")
        return is_ready
    def _start_reader(self):
        def read_pty_output():
            buffer = ""
            while True:
                try:
                    # 按行读取而不是按块读取
                    ready, _, _ = select.select([self.master], [], [], 0.1)
                    if ready:
                        data = os.read(self.master, 1024)
                        if data:
                            decoded = data.decode('utf-8', errors='ignore')
                            buffer += decoded
                            
                            # 按行处理缓冲区中的内容
                            while '\n' in buffer:
                                line, buffer = buffer.split('\n', 1)
                                #print(f"CLAUDE: {line}", flush=True)
                                if "? for shortcuts" in line.strip():
                                    print(f"CLAUDE: {line}", flush=True)
                                self.output_queue.put(line)
                                
                        else:
                            # 处理缓冲区中剩余的内容
                            if buffer:
                                print(f"CLAUDE: {buffer}", flush=True)
                                self.output_queue.put(buffer)
                                buffer = ""  # 清空缓冲区
                            break
                    elif not ready:
                        # 即使没有数据可读，也要处理缓冲区中可能剩余的内容
                        if buffer and not buffer.endswith('\n'):
                            # 检查buffer内容是否为可见字符
                            if not all(c in string.printable for c in buffer):
                                #print(f"CLAUDE: {buffer}", flush=True)
                                self.output_queue.put(buffer)
                            buffer = ""  # 清空缓冲区
                        # 短暂休眠以避免高CPU占用
                        time.sleep(0.01)
                except OSError:
                    # 处理缓冲区中剩余的内容
                    if buffer:
                        print(f"CLAUDE ERROR: {buffer}", flush=True)
                        self.output_queue.put(buffer)
                        buffer = ""  # 清空缓冲区
                    break
                except Exception as e:
                    # 处理缓冲区中剩余的内容
                    if buffer:
                        print(f"CLAUDE ERROR: {buffer}", flush=True)
                        self.output_queue.put(buffer)
                        buffer = ""  # 清空缓冲区
                    break
        
        t = threading.Thread(target=read_pty_output, daemon=True)
        t.start()
    
    def _send_command(self, command):
        if self.process and self.master:
            # 通过pty的master端口发送命令，而不是stdin
            os.write(self.master, (command + '\n').encode('utf-8'))

    def _get_output(self, timeout=60) -> List[str]:
        lines = []
        start = time.time()
        while True:
            try:
                line = self.output_queue.get(timeout=timeout)
                lines.append(line)
                # 增加对 Claude 提示符的检测
                if "? for shortcuts" in line.strip():
                    break
                if time.time() - start > timeout:
                    break
            except queue.Empty:
                break
        return lines
    def _clear_output_queue(self):
        """清空输出队列中的所有消息"""
        while not self.output_queue.empty():
            try:
                self.output_queue.get_nowait()
            except queue.Empty:
                break

    def close(self):
        if self.process:
            self._send_command('quit')
            self.process.terminate()    
    async def _wait_for_ready(self) -> bool:
        """等待Claude完全准备就绪"""
        try:
            logger.debug("等待Claude准备就绪...")
            start_time = time.time()

            # 读取初始输出直到看到(Claude)提示符
            while time.time() - start_time < self._init_timeout:
                if self.process.poll() is not None:
                    logger.error("Claude进程在初始化期间退出")
                    return False
                
                lines = self._get_output(self._init_timeout)
                for line in lines:
                    #print(f" ==={line}")
                    if line and "? for shortcuts" in line.strip():
                        # 检查是否看到Claude提示符，表示准备就绪
                        logger.debug("Claude会话准备就绪")
                        self.is_connected = True
                        return True
                    # 如果看到错误信息，记录但继续等待
                    # if line.startswith("^error"):
                    #     logger.warning(f"Claude初始化时出现错误: {line}")
                # 短暂休眠
                await asyncio.sleep(0.1)

            # 超时但没有看到提示符，尝试发送一个简单命令测试
            logger.error("未在预期时间内看到Claude提示符...")

        except BaseException as e:
            logger.error(f"等待Claude准备就绪失败: {e}")
        
        return False
    
    def execute_command(self, command: str, command_timeout:int = 1) -> List[str]:
        """执行Claude命令"""
        if not self.is_connected or not self.process:
            raise RuntimeError("Claude会话未连接")
        
        try:
            start_time = time.time()
            event = (f"执行Claude命令: {command}")
            #logger.debug(f"执行Claude命令: {command}")
            # 清理之前命令的输出
            self._clear_output_queue()
            self._send_command(command)
            
            # try:
            #     await asyncio.sleep(0.1)
            # except BaseException:
            #     pass
            
            output = self._get_output(command_timeout)
            #logger.info(f"Claude-OUT: {output}")
            event = f"{event}, 输出: {output}"
            return output
        except BaseException as e:
            logger.error(f"执行Claude命令失败: {e}")
            return [""]
        
    # 读取控制台已有的输出
    async def clear_console(self, timeout: int = 5) -> str:
        try:
            start_time = time.time()
            consecutive_empty_reads = 0

            while True:
                # 超时检查
                if time.time() - start_time > timeout:
                    break

                # 进程状态检查
                if self.process.poll() is not None:
                    logger.error("Claude进程已退出")
                    break

                # 读取一行
                try:
                    line = await self._read_line()
                    if line:
                        logger.info(f"Claude输出: {line}")
                        consecutive_empty_reads = 0
                    else:
                        consecutive_empty_reads += 1
                        # 如果连续多次读取为空，且已有输出，可能命令已完成
                        if consecutive_empty_reads >= 10 :
                            logger.debug(f"连续{consecutive_empty_reads}次空读取，可能命令已完成")
                            break

                except BaseException as e:
                    logger.error(f"读取行失败: {e}")
                    consecutive_empty_reads += 1
                    continue

                # 短暂休眠，避免CPU占用过高
                await asyncio.sleep(0.01)

            return ""

        except BaseException as e:
            logger.error(f"读取Claude输出失败: {e}")
            return ""
    
    async def _read_line(self) -> str:
        """读取一行输出，带超时控制"""
        if not self.process or not self.process.stdout:
            return ""

        try:
            # 使用asyncio.wait_for添加超时控制
            loop = asyncio.get_event_loop()

            # 设置单行读取超时为1秒，避免无限期阻塞
            line = await asyncio.wait_for(
                loop.run_in_executor(None, self._readline_with_timeout),
                timeout=1.0
            )
            line = line.strip() if line else ""
            if line:
                logger.info(f"Claude-OUT: {line}")
            return line

        except BaseException as e:
            logger.error(f"读取行时发生异常: {e}")
            return ""

    def _readline_with_timeout(self) -> str:
        """带超时的readline实现"""
        if not self.process or not self.process.stdout:
            return ""

        try:
            # 检查是否有数据可读
            import select

            # 在Unix系统上使用select检查是否有数据可读
            if hasattr(select, 'select'):
                ready, _, _ = select.select([self.process.stdout], [], [], 0.1)
                if ready:
                    return self.process.stdout.readline()
                else:
                    return ""
            else:
                # Windows系统回退到直接读取
                return self.process.stdout.readline()

        except BaseException as e:
            logger.error(f"readline_with_timeout异常: {e}")
            return ""
    
    async def close_session(self):
        """关闭Claude会话"""
        try:
            if self.process:
                logger.info("关闭Claude会话")

                # 计算会话持续时间
                session_duration = 0
                if self._session_start_time:
                    session_duration = time.time() - self._session_start_time

                # 尝试优雅退出
                exit_code = None
                try:
                    await self.execute_command("/quit")
                    await asyncio.sleep(1)
                    exit_code = self.process.poll()
                except:
                    pass

                # 强制终止进程
                if self.process.poll() is None:
                    self.process.terminate()
                    await asyncio.sleep(1)

                    if self.process.poll() is None:
                        self.process.kill()
                        exit_code = -9  # SIGKILL
                    else:
                        exit_code = self.process.poll()

                self.process = None
                self.is_connected = False

                logger.info(f"Claude会话已关闭，用时:{session_duration}")

        except BaseException as e:
            logger.error(f"关闭Claude会话失败: {e}")

    def get_sessinon_id(self):
        out_lines = self.execute_command("/status")
        # 从输出中解析 L Session ID:: d103531f-eb9f-48c2-adc9-c0c43f52247e
        for line in out_lines:
            if "Session ID:" in line:
                return line.split(":")[1].strip()
        return ""

async def main():
    session = ClaudeSession()
    # work_dir = "/mnt/d/aicode/intelliTest/data/code/a96ca61c-c234-49b0-8603-c8c1cd2f9771"
    # session_id = "7533deb1-5fd7-465c-aa34-bc2e9123c0f6"
    work_dir = "/ddrive/aicode/intelliTest/data/code/a96ca61c-c234-49b0-8603-c8c1cd2f9771"
    session_id = "79e01bea-0200-4681-9a1d-c5602dfd71de"
    if not await session.start_session(work_dir, session_id):
        return
    new_session_id = session.get_sessinon_id()
    print(f"new_session_id = {new_session_id}")
    await session.close_session()
    
if __name__ == "__main__":
    asyncio.run(main())