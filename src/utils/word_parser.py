#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
V10签名验签服务器接口设计Word文档解析器
解析Word文档中的表格形式接口定义，并输出为JSON格式
"""

import re
import json
import sys
import os
import html
from typing import List, Dict, Any, Optional
from pathlib import Path
import logging
from datetime import datetime

try:
    from docx import Document
    from docx.table import Table
    from docx.text.paragraph import Paragraph
except ImportError:
    print("需要安装python-docx库：pip install python-docx")
    exit(1)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class WordInterfaceParser:
    """Word接口文档解析器"""
    
    def __init__(self, doc_path: str):
        self.doc_path = Path(doc_path)
        self.interfaces = []
        self.current_section = ""
        
    def read_document(self) -> Document:
        """读取Word文档"""
        try:
            doc = Document(self.doc_path)
            logger.info(f"成功读取Word文档，包含 {len(doc.paragraphs)} 个段落和 {len(doc.tables)} 个表格")
            return doc
        except Exception as e:
            logger.error(f"读取Word文档失败: {e}")
            return None
    
    def extract_sections_from_doc(self, doc: Document) -> List[Dict[str, Any]]:
        """从Word文档中提取章节信息"""
        sections = []
        current_section = ""
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            
            # 识别章节标题（数字开头的标题）
            if self.is_section_title(text):
                section_info = self.parse_section_title(text)
                if section_info:
                    sections.append({
                        'number': section_info['number'],
                        'title': section_info['title'],
                        'full_title': text,
                        'paragraph_index': i
                    })
        
        logger.info(f"识别到 {len(sections)} 个章节")
        return sections
    
    def is_section_title(self, text: str) -> bool:
        """判断是否为章节标题"""
        # 扩展章节识别模式，支持更多格式
        patterns = [
            r'^\d+(?:\.\d+)*\.\s*.+',  # *******. 标题
            r'^\d+(?:\.\d+)*\s+.+',   # ******* 标题（没有点）
            r'^第\d+章\s*.+',           # 第1章 标题
            r'^\d+、.+',               # 1、标题
            r'^[A-Z][a-zA-Z\s]{3,}$',    # 英文标题（如 Java接口定义）
            r'^中文[\u4e00-\u9fff\s]{2,}$',  # 中文标题（如 SDK初始化）
        ]
        
        # 直接匹配常见的功能章节名称
        section_names = [
            'SDK初始化', 'Java接口定义', '前置说明', '约定', '服务设置',
            '证书管理', '密钥管理', '密码运算', '非对称算法', 
            '消息签名验签', '签名验签', 'PKCS7操作', '数字信封',
            'XML操作', 'PDF操作', 'OFD操作', '条形码', '时间戳',
            '电子签章', '监控', '工具类', '双证书', '单证书'
        ]
        
        # 先检查直接匹配
        if text.strip() in section_names:
            return True
        
        # 再检查模式匹配
        for pattern in patterns:
            if re.match(pattern, text):
                return True
                
        # 检查是否为短的功能性标题（3-20个字符）
        if 3 <= len(text.strip()) <= 20 and not any(char in text for char in ['、', '。', '：', '；']):
            # 不包含句号等标点，可能是标题
            return True
            
        return False
    
    def parse_section_title(self, text: str) -> Optional[Dict[str, str]]:
        """解析章节标题"""
        patterns = [
            r'^(\d+(?:\.\d+)*)\.\s*(.+)',  # *******. 标题
            r'^(\d+(?:\.\d+)*)\s+(.+)',   # ******* 标题
            r'^第(\d+)章\s*(.+)',           # 第1章 标题
            r'^(\d+)、(.+)',               # 1、标题
        ]
        
        for pattern in patterns:
            match = re.match(pattern, text)
            if match:
                return {
                    'number': match.group(1),
                    'title': match.group(2).strip()
                }
        
        # 如果没有找到数字编号，就把整个文本作为标题
        text = text.strip()
        if text:
            return {
                'number': '0',
                'title': text
            }
        return None
    
    def extract_interfaces_from_doc(self, doc: Document) -> List[Dict[str, Any]]:
        """从Word文档中提取接口定义"""
        interfaces = []
        type_definitions = []  # 新增：存储类型定义
        sections = self.extract_sections_from_doc(doc)
        
        # 直接使用文档中的章节标题
        section_mapping = self.build_section_mapping(doc)
        
        table_count = 0
        for table in doc.tables:
            table_count += 1
            
            # 检查是否为类型定义表格
            type_definition = self.extract_type_definition(table)
            if type_definition:
                type_definitions.append(type_definition)
                continue
            
            # 直接使用章节标题来确定表格所属的章节
            table_section = self.find_table_section_by_position(doc, table, sections)
            
            # 解析表格内容
            interface_data = self.parse_interface_table(table, table_section)
            
            if interface_data and self.is_valid_interface(interface_data):
                interfaces.append(interface_data)
        
        # 将类型定义添加到结果中
        self.type_definitions = type_definitions
        
        logger.info(f"从 {len(doc.tables)} 个表格中提取到 {len(interfaces)} 个有效接口和 {len(type_definitions)} 个类型定义")
        return interfaces
    



    
    def build_section_mapping(self, doc: Document) -> Dict[int, str]:
        """构建段落位置到章节标题的映射"""
        section_mapping = {}
        current_section = "未分类接口"
        
        for i, paragraph in enumerate(doc.paragraphs):
            text = paragraph.text.strip()
            if self.is_section_title(text):
                section_info = self.parse_section_title(text)
                if section_info and section_info['title']:
                    current_section = section_info['title']
            section_mapping[i] = current_section
        
        return section_mapping
    
    def find_table_section_by_position(self, doc: Document, target_table: Table, sections: List[Dict]) -> str:
        """根据表格在文档中的位置找到最近的3级章节标题"""
        try:
            # 获取所有章节信息
            if not sections:
                return "未分类接口"
            
            # 查找表格之前最近的有意义的3级章节标题
            # 使用简化的方法：直接从表格内容中提取特征关键词
            table_content = ""
            for row in target_table.rows[:3]:  # 只检查前3行
                for cell in row.cells:
                    table_content += cell.text + " "
            
            # 在表格内容中查找匹配的章节关键词
            section_matches = []
            for section in sections:
                section_title = section.get('title', '')
                
                # 只考虑功能性章节
                if self.is_functional_section(section_title):
                    # 检查表格内容是否与章节相关
                    similarity = self.calculate_section_similarity(table_content, section_title)
                    if similarity > 0:
                        section_matches.append((similarity, section_title))
            
            # 返回最匹配的章节
            if section_matches:
                section_matches.sort(reverse=True)
                return section_matches[0][1]  # 返回章节标题
            
            # 如果没有找到匹配，使用第一个功能性章节
            for section in sections:
                section_title = section.get('title', '')
                if self.is_functional_section(section_title):
                    return section_title
            
            return "未分类接口"
            
        except Exception as e:
            logger.warning(f"表格章节匹配失败：{e}")
            return "未分类接口"
    
    def is_functional_section(self, section_title: str) -> bool:
        """判断是否为功能性章节标题"""
        # 过滤掉不是功能性的标题
        exclude_patterns = [
            r'^文档版本', r'^V\d+', r'^接口设计', r'^v\d+', r'^版权声明',
            r'^联系我们', r'^修订记录', r'^电话', r'^传真', r'^邮箱',
            r'^网址', r'^公司地址', r'^感谢您', r'^本文档由',
            r'^三未信安', r'^版权所有'
        ]
        
        for pattern in exclude_patterns:
            if re.match(pattern, section_title):
                return False
        
        # 包含功能关键词的才是功能性章节
        functional_keywords = [
            'SDK', '初始化', '证书', '密钥', '签名', '验签', '加密', '解密',
            '算法', '密码', '信封', '时间戳', '签章', '监控', 'PKCS', 'XML',
            'PDF', 'OFD', '条形码', '工具', '服务', '接口'
        ]
        
        return any(keyword in section_title for keyword in functional_keywords)

    
    def calculate_section_similarity(self, table_content: str, section_title: str) -> float:
        """计算表格内容与章节的相似度"""
        # 提取章节标题中的关键词
        section_keywords = re.findall(r'[\u4e00-\u9fff]+|[a-zA-Z]+', section_title)
        
        score = 0
        for keyword in section_keywords:
            if len(keyword) > 1 and keyword.lower() in table_content.lower():
                score += len(keyword)  # 较长的关键词权重更高
        
        return score





    
    def extract_type_definition(self, table: Table) -> Optional[Dict[str, Any]]:
        """提取类型定义（public class）"""
        type_def = {
            '类型': 'class',
            '名称': '',
            '描述': '',
            '定义': '',
            '属性': [],
            '方法': []
        }
        
        # 检查表格内容是否包含类定义
        has_class_definition = False
        class_definition_text = ""
        
        for row_idx, row in enumerate(table.rows):
            cells = [cell.text.strip() for cell in row.cells]
            
            # 检查是否包含 public class 关键词
            row_text = ' '.join(cells)
            if 'public class' in row_text.lower():
                has_class_definition = True
                class_definition_text = row_text
                
                # 提取类名
                class_match = re.search(r'public\s+class\s+(\w+)', row_text, re.IGNORECASE)
                if class_match:
                    type_def['名称'] = class_match.group(1)
                
                # 提取完整的类定义
                type_def['定义'] = self.clean_class_definition(row_text)
                break
        
        if not has_class_definition:
            return None
        
        # 继续解析类的属性和方法
        self.parse_class_members(table, type_def)
        
        # 提取类的描述（通常在第一行或接口功能字段）
        for row in table.rows:
            cells = [cell.text.strip() for cell in row.cells]
            if '接口功能' in cells[0] or '功能描述' in cells[0] or '说明' in cells[0]:
                type_def['描述'] = self.extract_unique_cell_content(cells[1:])
                break
        
        return type_def
    
    def clean_class_definition(self, text: str) -> str:
        """清理类定义文本"""
        # 移除多余的空白和重复内容
        text = re.sub(r'\s+', ' ', text)
        
        # 提取主要的类定义部分
        class_pattern = r'(public\s+class\s+\w+\s*\{[^}]*\})'  
        match = re.search(class_pattern, text, re.IGNORECASE | re.DOTALL)
        if match:
            return match.group(1).strip()
        
        # 如果没有找到完整的类定义，返回原文本
        return text.strip()
    
    def parse_class_members(self, table: Table, type_def: Dict[str, Any]):
        """解析类的成员（属性和方法）"""
        current_section = None
        
        for row_idx, row in enumerate(table.rows):
            cells = [cell.text.strip() for cell in row.cells]
            
            if not any(cells):
                continue
            
            first_cell = cells[0].strip()
            
            # 检查是否进入属性或方法区域
            if '属性' in first_cell or '参数名' in ' '.join(cells):
                current_section = 'properties'
                continue
            elif '方法' in first_cell:
                current_section = 'methods'
                continue
            
            # 解析属性
            if current_section == 'properties' and len(cells) >= 3:
                if cells[0] and cells[1] and cells[0] not in ['属性', '参数名', '类型']:
                    property_info = {
                        '名称': cells[0],
                        '类型': cells[1],
                        '必填': cells[2] if len(cells) > 2 else '',
                        '说明': self.extract_unique_cell_content(cells[3:]) if len(cells) > 3 else ''
                    }
                    type_def['属性'].append(property_info)
            
            # 解析方法
            elif current_section == 'methods' and len(cells) >= 2:
                if cells[0] and cells[0] not in ['方法', '方法名']:
                    method_info = {
                        '名称': cells[0],
                        '描述': self.extract_unique_cell_content(cells[1:]) if len(cells) > 1 else ''
                    }
                    type_def['方法'].append(method_info)
    
    def find_table_section(self, doc: Document, table: Table, sections: List[Dict]) -> str:
        """为表格找到对应的章节"""
        # 这是一个简化实现，实际中可能需要更复杂的逻辑来准确定位表格与章节的关系
        # 暂时返回一个默认章节
        try:
            table_index = doc.tables.index(table)
            if sections and table_index < len(sections):
                return sections[min(table_index, len(sections)-1)]['full_title']
        except ValueError:
            # 如果表格不在列表中，使用表格在文档中的位置来估算
            pass
        
        # 如果没有识别到章节或出现错误，返回默认值
        if sections:
            return sections[0]['full_title']
        return "接口定义表格"
    
    def parse_interface_table(self, table: Table, section_title: str) -> Optional[Dict[str, Any]]:
        """解析接口表格"""
        interface_data = {
            '章节': section_title,
            '需求编号': '',
            '需求名称': '',
            '优先级': '',
            '接口功能': '',
            '接口定义': '',
            '输入参数': [],
            '输出': '',
            '异常': '',
            '其它说明': '',
            '支持算法': []
        }
        
        current_field = None
        is_parameter_section = False
        
        # 遍历表格的所有行
        for row_idx, row in enumerate(table.rows):
            cells = [cell.text.strip() for cell in row.cells]
            
            if not any(cells):  # 跳过空行
                continue
            
            first_cell = cells[0].strip()
            
            # 检查是否为参数标题行
            if '参数名' in ' '.join(cells) and '类型' in ' '.join(cells):
                current_field = 'parameters'
                is_parameter_section = True
                continue
                
            # 检查是否为参数行
            elif (first_cell == '输入' and len(cells) >= 7 and 
                  cells[1] and cells[2] and cells[1] != '参数名'):
                # 这是一个参数行，直接解析
                param_name = cells[1].strip()
                param_type = cells[2].strip()
                param_required = cells[4].strip() if len(cells) > 4 else ''
                param_desc = self.extract_unique_cell_content(cells[6:] if len(cells) > 6 else [])
                
                if param_name and param_type:
                    parameter = {
                        '参数名': param_name,
                        '类型': param_type,
                        '必填': param_required,
                        '说明': self.clean_cell_text(param_desc)
                    }
                    interface_data['输入参数'].append(parameter)
                    
                    # 提取算法信息
                    if 'algorithm' in param_name.lower() and '算法' in param_desc:
                        algorithms = self.extract_algorithms_from_description(param_desc)
                        interface_data['支持算法'].extend(algorithms)
                        
            # 解析其他基本字段
            elif '需求编号' in first_cell and len(cells) >= 2:
                interface_data['需求编号'] = cells[1].strip()
            elif '需求名称' in ' '.join(cells[:4]):
                for i in range(min(len(cells), 8)):
                    if '需求名称' in cells[i] and i + 1 < len(cells):
                        interface_data['需求名称'] = cells[i + 1].strip()
                        break
            elif '优先级' in ' '.join(cells[:4]):
                for cell in cells:
                    if cell.strip() in ['高', '中', '低']:
                        interface_data['优先级'] = cell.strip()
                        break
            elif '接口功能' in first_cell:
                content = self.extract_unique_cell_content(cells[1:])
                interface_data['接口功能'] = self.clean_cell_text(content)
            elif '接口定义' in first_cell:
                content = self.extract_unique_cell_content(cells[1:])
                interface_data['接口定义'] = self.clean_cell_text(content)
            elif '输出' in first_cell and '参数名' not in ' '.join(cells):
                content = self.extract_unique_cell_content(cells[1:])
                interface_data['输出'] = self.clean_cell_text(content)
            elif '异常' in first_cell:
                content = self.extract_unique_cell_content(cells[1:])
                interface_data['异常'] = self.clean_cell_text(content)
            elif '其它说明' in first_cell or '其它说' in first_cell:
                content = self.extract_unique_cell_content(cells[1:])
                interface_data['其它说明'] = self.clean_cell_text(content)
        
        return interface_data
    
    def clean_cell_text(self, text: str) -> str:
        """清理单元格文本"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text).strip()
        
        # 处理特殊情况：如果文本太长且内容相同，只保留一份
        if len(text) > 100:
            # 将文本分成多个部分，检查是否有重复
            sentences = text.split('。')  # 按句号分割
            if len(sentences) == 1:
                sentences = text.split('.')  # 按英文句号分割
            
            if len(sentences) > 1:
                # 如果有多个句子，检查是否有重复
                first_sentence = sentences[0].strip()
                if first_sentence and len(first_sentence) > 10:
                    # 检查第一句话是否在整个文本中重复
                    count = text.count(first_sentence)
                    if count > 1:
                        # 如果重复，只保留第一部分
                        return first_sentence
        
        return text
    
    def parse_table_row(self, cells: List[str], interface_data: Dict[str, Any], 
                       current_field: str, is_parameter_section: bool):
        """解析表格行"""
        if not cells:
            return
        
        first_cell = cells[0].strip()
        
        # 解析基本字段
        if '需求编号' in first_cell and len(cells) >= 2:
            interface_data['需求编号'] = cells[1].strip()
        elif '需求名称' in ' '.join(cells[:4]):
            # 查找需求名称值（可能在不同位置）
            for i in range(min(len(cells), 8)):
                if '需求名称' in cells[i] and i + 1 < len(cells):
                    interface_data['需求名称'] = cells[i + 1].strip()
                    break
        elif '优先级' in ' '.join(cells[:4]) or '优先' in ' '.join(cells[:4]):
            # 查找优先级值
            for cell in cells:
                if cell.strip() in ['高', '中', '低']:
                    interface_data['优先级'] = cell.strip()
                    break
        elif '接口功能' in first_cell:
            # 提取接口功能描述（去除重复）
            content = self.extract_unique_cell_content(cells[1:])
            interface_data['接口功能'] = self.clean_cell_text(content)
        elif '接口定义' in first_cell:
            # 提取接口定义（去除重复）
            content = self.extract_unique_cell_content(cells[1:])
            interface_data['接口定义'] = self.clean_cell_text(content)
        elif '输入' in first_cell and '参数名' not in ' '.join(cells):
            # 进入参数区域，但不是标题行
            if len(cells) >= 7 and cells[1].strip() and cells[2].strip() and cells[1] != '参数名':
                # 这是一个参数行，直接处理
                self.parse_parameter_row(cells, interface_data)
        elif current_field == 'parameters' and is_parameter_section:
            self.parse_parameter_row(cells, interface_data)
        elif '输出' in first_cell and '参数名' not in ' '.join(cells):
            # 提取输出信息（去除重复）
            content = self.extract_unique_cell_content(cells[1:])
            interface_data['输出'] = self.clean_cell_text(content)
            current_field = 'output'
            is_parameter_section = False
        elif '异常' in first_cell:
            # 提取异常信息（去除重复）
            content = self.extract_unique_cell_content(cells[1:])
            interface_data['异常'] = self.clean_cell_text(content)
            current_field = 'exception'
            is_parameter_section = False
        elif '其它说明' in first_cell or '其它说' in first_cell:
            # 提取其它说明（去除重复）
            content = self.extract_unique_cell_content(cells[1:])
            interface_data['其它说明'] = self.clean_cell_text(content)
    
    def parse_parameter_row(self, cells: List[str], interface_data: Dict[str, Any]):
        """解析参数行"""
        if len(cells) < 4:
            return
        
        # 跳过标题行
        if '参数名' in cells[0] or '参数名' in cells[1]:
            return
        
        # 跳过非参数行
        first_cell = cells[0].strip()
        if first_cell in ['输入', '输出', '异常', '接口功能', '接口定义', ''] or '输出' in first_cell or '异常' in first_cell:
            return
        
        # 对于输入参数行，第一列可能是"输入"，第二列才是参数名
        param_name = ""
        param_type = ""
        param_required = ""
        param_desc = ""
        
        if first_cell == '输入' and len(cells) >= 7:
            # 这是一个输入参数行，格式可能是：[输入, 参数名, 类型, 类型, 必填, 必填, 说明, ...]
            param_name = cells[1].strip()
            param_type = cells[2].strip()
            param_required = cells[4].strip() if len(cells) > 4 else ''
            # 说明可能在后续的多个单元格中（由于合并单元格）
            desc_cells = cells[6:] if len(cells) > 6 else []
            param_desc = self.extract_unique_cell_content(desc_cells)
        else:
            # 普通参数行
            param_name = cells[0].strip()
            param_type = cells[1].strip() if len(cells) > 1 else ''
            param_required = cells[2].strip() if len(cells) > 2 else ''
            desc_cells = cells[3:] if len(cells) > 3 else []
            param_desc = self.extract_unique_cell_content(desc_cells)
        
        # 只有当参数名和类型都不为空时才添加
        if param_name and param_type and param_name not in ['输入', '输出', '异常', '参数名']:
            parameter = {
                '参数名': param_name,
                '类型': param_type,
                '必填': param_required,
                '说明': self.clean_cell_text(param_desc)
            }
            interface_data['输入参数'].append(parameter)
            
            # 提取算法信息
            if 'algorithm' in param_name.lower() and '算法' in param_desc:
                algorithms = self.extract_algorithms_from_description(param_desc)
                interface_data['支持算法'].extend(algorithms)
    
    def extract_algorithms_from_description(self, description: str) -> List[str]:
        """从描述中提取算法"""
        algorithms = set()
        
        # 算法模式
        patterns = [
            r'(SHA\d+WithRSA(?:/PSS)?)',
            r'(SM3WithSM2)',
            r'(NoneWithRSA)',
            r'(NoneWithSM2)',
            r'(SHA\d+WithECDSA)',
            r'(NoneWithECDSA)',
            r'(ED25519)',
            r'(ED448)',
            r'(ML-[A-Z]+\d*)',
            r'(SLH-DSA)',
            r'(AES\d+)',
            r'(DES)',
            r'(3DES)',
            r'(SM[1-9])',
            r'(SHA\d+)',
            r'(MD5)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, description, re.IGNORECASE)
            algorithms.update(matches)
        
        return list(algorithms)
    
    def extract_unique_cell_content(self, cells: List[str]) -> str:
        """从多个可能重复的单元格中提取唯一内容"""
        if not cells:
            return ""
        
        # 去除空单元格
        non_empty_cells = [cell.strip() for cell in cells if cell.strip()]
        if not non_empty_cells:
            return ""
        
        # 如果只有一个非空单元格，直接返回
        if len(non_empty_cells) == 1:
            return non_empty_cells[0]
        
        # 如果所有单元格内容相同，返回一个
        if all(cell == non_empty_cells[0] for cell in non_empty_cells):
            return non_empty_cells[0]
        
        # 如果内容不同，选择最长的一个（通常是最完整的）
        return max(non_empty_cells, key=len)
    
    def is_parameter_header_row(self, cells: List[str]) -> bool:
        """判断是否为参数标题行"""
        # 检查是否包含参数表格的标题
        return ('参数名' in ' '.join(cells) and 
                ('类型' in ' '.join(cells) or '必填' in ' '.join(cells)))
    
    def is_output_row(self, cells: List[str]) -> bool:
        """判断是否为输出行"""
        return (cells and '输出' in cells[0] and 
                '参数名' not in ' '.join(cells))
    
    def is_exception_row(self, cells: List[str]) -> bool:
        """判断是否为异常行"""
        return (cells and '异常' in cells[0] and 
                '参数名' not in ' '.join(cells))
    
    def is_valid_interface(self, interface_data: Dict[str, Any]) -> bool:
        """验证接口数据是否有效"""
        return (interface_data.get('需求编号') and 
                (interface_data.get('接口定义') or interface_data.get('接口功能')))
    
    def categorize_interfaces(self, interfaces: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按章节标题直接分类接口"""
        categories = {}
        
        # 直接按章节字段分类
        for interface in interfaces:
            section = interface.get('章节', '')
            
            # 使用实际的章节名称作为分类
            if section:
                # 精简章节名称，移除编号
                category_name = self.simplify_section_name(section)
            else:
                category_name = '未分类接口'
            
            # 创建分类（如果不存在）
            if category_name not in categories:
                categories[category_name] = []
            
            categories[category_name].append(interface)
        
        return categories
    
    def simplify_section_name(self, section: str) -> str:
        """根据功能性内容创建3级分类体系"""
        # 移除章节编号
        simplified = re.sub(r'^\d+(?:\.\d+)*\.?\s*', '', section)
        simplified = re.sub(r'^第\d+章\s*', '', simplified)
        simplified = re.sub(r'^\d+、\s*', '', simplified)
        
        content_lower = simplified.lower()
        
        # 定义3级分类体系：主分类 -> 子分类 -> 功能点
        
        # 1. SDK与初始化类
        if any(keyword in content_lower for keyword in ['sdk', '初始化', '配置', 'init', '连接']):
            if '初始化' in content_lower or 'init' in content_lower:
                return '1.1 SDK初始化'
            elif '连接' in content_lower or '断开' in content_lower:
                return '1.2 连接管理'
            else:
                return '1.3 SDK配置'
        
        # 2. 证书管理类
        elif any(keyword in content_lower for keyword in ['证书', 'cert', 'certificate', '信任域', 'pfx', 'csr']):
            if '信任域' in content_lower:
                return '2.1 信任域证书管理'
            elif '业务' in content_lower or 'pfx' in content_lower or 'csr' in content_lower:
                return '2.2 业务证书管理'
            else:
                return '2.3 证书通用操作'
        
        # 3. 密钥管理类
        elif any(keyword in content_lower for keyword in ['密钥', 'key']):
            if '对称' in content_lower:
                return '3.1 对称密钥管理'
            elif '非对称' in content_lower:
                return '3.2 非对称密钥管理'
            else:
                return '3.3 密钥通用操作'
        
        # 4. 密码运算类
        elif any(keyword in content_lower for keyword in ['加密', '解密', '密码', 'mac', '哈希', '转换']):
            if 'mac' in content_lower:
                return '4.1 MAC运算'
            elif '哈希' in content_lower or 'hash' in content_lower:
                return '4.2 哈希运算'
            elif '加密' in content_lower or '解密' in content_lower:
                return '4.3 对称加解密'
            elif '转换' in content_lower:
                return '4.4 密钥格式转换'
            else:
                return '4.5 其他密码运算'
        
        # 5. 签名验签类
        elif any(keyword in content_lower for keyword in ['签名', '验签', 'sign', 'verify']):
            if '数据' in content_lower:
                return '5.1 数据签名验签'
            elif '文件' in content_lower:
                return '5.2 文件签名验签'
            else:
                return '5.3 消息签名验签'
        
        # 6. PKCS操作类
        elif any(keyword in content_lower for keyword in ['pkcs', 'p7', 'p12', 'attached', 'detached']):
            if 'attached' in content_lower:
                return '6.1 Attached签名验签'
            elif 'detached' in content_lower:
                return '6.2 Detached签名验签'
            else:
                return '6.3 PKCS标准操作'
        
        # 7. 数字信封类
        elif any(keyword in content_lower for keyword in ['信封', 'envelope']):
            if '带签名' in content_lower:
                return '7.1 带签名数字信封'
            else:
                return '7.2 普通数字信封'
        
        # 8. 文档签名类
        elif any(keyword in content_lower for keyword in ['xml', 'pdf', 'ofd']):
            if 'xml' in content_lower:
                return '8.1 XML签名验签'
            elif 'pdf' in content_lower:
                return '8.2 PDF签名验签'
            elif 'ofd' in content_lower:
                return '8.3 OFD签名验签'
            else:
                return '8.4 文档签名通用'
        
        # 9. 特殊服务类
        elif any(keyword in content_lower for keyword in ['时间戳', 'timestamp', '签章', '印章', '条形码', '条码']):
            if '时间戳' in content_lower or 'timestamp' in content_lower:
                return '9.1 时间戳服务'
            elif '签章' in content_lower or '印章' in content_lower:
                return '9.2 电子签章'
            elif '条形码' in content_lower or '条码' in content_lower:
                return '9.3 条形码处理'
            else:
                return '9.4 其他特殊服务'
        
        # 10. 系统管理类
        elif any(keyword in content_lower for keyword in ['监控', 'monitor', '设备', '工具', 'util', 'base64', '转换', '解析']):
            if '监控' in content_lower or 'monitor' in content_lower:
                return '10.1 系统监控'
            elif '工具' in content_lower or 'util' in content_lower or 'base64' in content_lower:
                return '10.2 工具类接口'
            else:
                return '10.3 系统管理'
        
        # 默认分类
        else:
            # 限制长度
            if len(simplified) > 15:
                simplified = simplified[:15] + '...'
            return f'11.0 {simplified}' if simplified else '11.0 其他操作'


    
    def generate_statistics(self, interfaces: List[Dict[str, Any]], 
                          categorized: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """生成统计信息"""
        return {
            '总接口数': len(interfaces),
            '分类统计': {k: len(v) for k, v in categorized.items() if v},
            '算法统计': self.count_algorithms(interfaces),
            '参数统计': self.count_parameters(interfaces)
        }
    
    def count_algorithms(self, interfaces: List[Dict[str, Any]]) -> Dict[str, int]:
        """统计算法使用情况"""
        algorithm_count = {}
        for interface in interfaces:
            for algo in interface.get('支持算法', []):
                algorithm_count[algo] = algorithm_count.get(algo, 0) + 1
        return dict(sorted(algorithm_count.items(), key=lambda x: x[1], reverse=True))
    
    def count_parameters(self, interfaces: List[Dict[str, Any]]) -> Dict[str, Any]:
        """统计参数情况"""
        total_params = sum(len(interface.get('输入参数', [])) for interface in interfaces)
        interfaces_with_params = sum(1 for interface in interfaces if interface.get('输入参数'))
        
        return {
            '总参数数': total_params,
            '有参数的接口数': interfaces_with_params,
            '平均参数数': round(total_params / len(interfaces), 2) if interfaces else 0
        }
    
    def parse_and_export(self, output_path: str = None) -> Dict[str, Any]:
        """解析Word文档并导出JSON"""
        logger.info("开始解析Word文档...")
        
        # 读取文档
        doc = self.read_document()
        if not doc:
            return {}
        
        # 提取接口
        interfaces = self.extract_interfaces_from_doc(doc)
        logger.info(f"提取到 {len(interfaces)} 个接口")
        
        # 分类接口
        categorized = self.categorize_interfaces(interfaces)
        
        # 生成统计信息
        stats = self.generate_statistics(interfaces, categorized)
        
        # 获取类型定义（如果有）
        type_definitions = getattr(self, 'type_definitions', [])
        
        result = {
            '文档信息': {
                '文档名称': 'V10签名验签服务器接口设计V0.6',
                '文档格式': 'Word文档',
                '解析时间': self.get_current_time(),
                '文档路径': str(self.doc_path),
                '表格数量': len(doc.tables) if doc else 0
            },
            '统计信息': stats,
            '类型定义': type_definitions,  # 新增类型定义区域
            '分类接口': categorized,
            '所有接口': interfaces
        }
        
        # 导出到文件
        if output_path:
            self.export_to_json(result, output_path)
        
        return result
    
    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    def export_to_json(self, data: Dict[str, Any], output_path: str):
        """导出到JSON文件"""
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            logger.info(f"解析结果已导出到: {output_path}")
        except Exception as e:
            logger.error(f"导出JSON文件失败: {e}")


def main():
    """主函数"""
    # 从参数获取文档路径
    if len(sys.argv) > 1:
        doc_path = sys.argv[1]
    else:
        doc_path = r"V10签名验签服务器接口设计V0.6.docx"
    
    if not os.path.exists(r"V10签名验签服务器接口设计V0.6.docx"):
        print("请指定有效的Word文档路径")
        exit(1) 
    # 与word同名的json文件
    json_path = os.path.splitext(doc_path)[0] + ".json"

    # 检查是否有--verbose参数
    verbose = '--verbose' in sys.argv
    
    # 创建解析器
    parser = WordInterfaceParser(doc_path)
    
    # 解析并导出
    result = parser.parse_and_export(json_path)
    
    # 打印统计信息
    print("\n=== Word文档解析统计 ===")
    doc_info = result.get('文档信息', {})
    print(f"文档名称: {doc_info.get('文档名称', '')}")
    print(f"文档格式: {doc_info.get('文档格式', '')}")
    print(f"表格数量: {doc_info.get('表格数量', 0)}")
    
    stats = result.get('统计信息', {})
    print(f"总接口数: {stats.get('总接口数', 0)}")
    
    if not verbose:
        return
    
    print("\n分类统计:")
    for category, count in stats.get('分类统计', {}).items():
        print(f"  {category}: {count} 个接口")
    
    print(f"\n参数统计:")
    param_stats = stats.get('参数统计', {})
    print(f"  总参数数: {param_stats.get('总参数数', 0)}")
    print(f"  有参数的接口数: {param_stats.get('有参数的接口数', 0)}")
    print(f"  平均参数数: {param_stats.get('平均参数数', 0)}")
    
    print(f"\n算法统计(前10):")
    algo_stats = stats.get('算法统计', {})
    for i, (algo, count) in enumerate(list(algo_stats.items())[:10]):
        print(f"  {algo}: {count} 次")
    
    # 显示类型定义统计
    type_definitions = result.get('类型定义', [])
    if type_definitions:
        print(f"\n类型定义统计: {len(type_definitions)} 个类型定义")
        for i, type_def in enumerate(type_definitions[:3]):
            print(f"  {i+1}. {type_def.get('名称', '')} ({type_def.get('类型', '')})")
            if type_def.get('属性'):
                print(f"     属性数: {len(type_def.get('属性', []))}")
            if type_def.get('方法'):
                print(f"     方法数: {len(type_def.get('方法', []))}")
    
    # 显示部分接口示例
    interfaces = result.get('所有接口', [])
    if interfaces:
        print(f"\n=== 接口示例 (前3个) ===")
        for i, interface in enumerate(interfaces[:3]):
            print(f"\n{i+1}. {interface.get('需求编号', '')} - {interface.get('需求名称', '')}")
            print(f"   章节: {interface.get('章节', '')}")
            print(f"   功能: {interface.get('接口功能', '')[:80]}...")
            print(f"   参数数: {len(interface.get('输入参数', []))}")
            if interface.get('支持算法'):
                print(f"   支持算法: {', '.join(interface.get('支持算法', [])[:3])}")


if __name__ == "__main__":
    main()