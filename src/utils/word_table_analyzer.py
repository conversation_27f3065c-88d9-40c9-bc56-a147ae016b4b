#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Word文档表格结构分析工具
专门用于分析PR-F-SVS-0090等接口的表格结构
"""

import re
from docx import Document
from pathlib import Path

def analyze_table_structure(doc_path: str, target_interface: str = "PR-F-SVS-0090"):
    """分析特定接口的表格结构"""
    doc = Document(doc_path)
    
    print(f"=== 分析目标接口: {target_interface} ===")
    print(f"文档包含 {len(doc.tables)} 个表格")
    
    target_table = None
    table_index = -1
    
    # 查找包含目标接口的表格
    for i, table in enumerate(doc.tables):
        table_text = ""
        for row in table.rows:
            for cell in row.cells:
                table_text += cell.text + " "
        
        if target_interface in table_text:
            target_table = table
            table_index = i
            print(f"\n找到目标接口，位于表格 #{i}")
            break
    
    if target_table is None:
        print(f"未找到包含 {target_interface} 的表格")
        return
    
    print(f"\n=== 表格 #{table_index} 详细结构分析 ===")
    print(f"表格包含 {len(target_table.rows)} 行 x {len(target_table.columns)} 列")
    
    # 分析每一行的结构
    for row_idx, row in enumerate(target_table.rows):
        print(f"\n--- 第 {row_idx} 行 ({len(row.cells)} 个单元格) ---")
        for cell_idx, cell in enumerate(row.cells):
            cell_text = cell.text.strip()
            if cell_text:
                # 限制显示长度
                display_text = cell_text[:50] + "..." if len(cell_text) > 50 else cell_text
                print(f"  单元格 [{cell_idx}]: {display_text}")
            else:
                print(f"  单元格 [{cell_idx}]: [空]")
    
    print(f"\n=== 完整表格内容 ===")
    for row_idx, row in enumerate(target_table.rows):
        row_data = []
        for cell in row.cells:
            cell_text = cell.text.strip().replace('\n', ' ').replace('\r', ' ')
            row_data.append(f'"{cell_text}"')
        print(f"第{row_idx}行: [{', '.join(row_data)}]")

if __name__ == "__main__":
    doc_path = r"D:\WORK\claude-demo\V10签名验签服务器接口设计V0.6.docx"
    analyze_table_structure(doc_path)