import re
import os
from claude_code_sdk import ClaudeSDKClient, Claude<PERSON>odeOptions,ResultMessage
from typing import Dict, List, Optional, Any, Callable, Union
from loguru import logger
from ..config import Config
from .word_parser import WordInterfaceParser

class ClaudeAgent:
    """ClaudeCode 智能体"""
    def __init__(self, config:Config):
        self.config = config
        self.code_path = os.path.join(config.DATA_PATH, 'code')
        self.prompt_path = config.PROMPT_PATH
        # 加载系统提示词
        try:
            with open(os.path.join(config.PROMPT_PATH,"java_test_prompt.md"), 'r', encoding='utf-8') as f:
                self.system_prompt = f.read()
        except Exception as e:
            logger.error(f"加载系统提示词失败: {str(e)}")
            self.system_prompt = "分析用户提供的接口文档，根据接口文档生成java testng测试用例和各种测试数据"
    
    async def generate_test_cases(self, project: Dict,  progress_callback=None
    ) -> Dict[str, Any]:
        # 检查项目目录下是否有md格式的接口文档
        project_dir = os.path.join(self.code_path, project['id'])
        # 过滤临时文件: ~开头的文件 和非word文件
        doc_files = [f for f in os.listdir(project_dir) if f.endswith(tuple([".doc","docx"]))]
        doc_files = [f for f in doc_files if not f.startswith('~')]
        if not doc_files:
            return {
                'success': False,                
                "message": "没有接口文档."
                }
        
        # 解析接口文档，提取json结构的接口定义
        all_categorized = {}
        total_interfaces = 0
        completed_interfaces = 0
        for doc_file in doc_files:# 查看是否有同名的json文件
            parser = WordInterfaceParser(os.path.join(project_dir, doc_file))
            json_result = parser.parse_and_export()
            # 提取'分类接口' 加到all_categorized中
            all_categorized.update(json_result.get('分类接口', {}))
            total_interfaces += json_result.get('统计信息', {}).get('总接口数', 0) * 2 # 接口数+测试数据
            
        # 将提示词目录下的claude配置文件赋值到项目目录的.claude目录（没有则创建）
        claude_conf_dir = os.path.join(project_dir, '.claude')
        os.makedirs(claude_conf_dir, exist_ok=True)
        os.system(f"cp -r {self.prompt_path}/{project['language']}/* {claude_conf_dir}")
                
        total_progress = 0
        def compute_progress(inc: int, type:str, message: str=''):
            nonlocal total_progress, completed_interfaces
            if type == "ToolResultBlock" and "created successfully" in message:
                if ".java" in message or ".csv" in message:
                    # 计算进度
                    completed_interfaces += 1
                    total_progress = int(min(completed_interfaces / total_interfaces * 100, 100))
            progress_callback(total_progress, f"{type}: {message[:50]}")
                    
            
        session_id = "32b39c4f-2546-44d7-b5f6-dbe4f4027e98"
        # 汇总所有分类的接口
        # all_interfaces = []
        # for category, interfaces in all_categorized.items():
        #     all_interfaces.extend(interfaces)
        # user_req = f"接口定义：{all_interfaces}"
        # result = await self.run_agent(project_dir, self.system_prompt, user_req, session_id, compute_progress)
        
        for category, interfaces in all_categorized.items():
            if not interfaces:
                continue
            print(f"开始处理接口分类：{category}")
            user_req = f"接口分类：{category}\n\n接口定义：{interfaces}"
            #user_req = "介绍自己"
            result = await self.run_agent(project_dir, self.system_prompt, user_req, session_id, compute_progress)
            session_id = result.get('session_id')
            
        return result
    async def run_agent(self, cwd_dir , system_prompt, user_req, session_id = None, progress_callback=None
    ) -> Dict[str, Any]:
        os.environ["IS_SANDBOX"] = "1"
                
        # 清理之前的会话
        # claude -p "/clear"
        
        # 最大长度
        # BASH_MAX_OUTPUT_LENGTH
        
        # 设置环境变量： IS_SANDBOX=1
        continue_conversation = session_id is not None
        client = ClaudeSDKClient(options=ClaudeCodeOptions(
                system_prompt = system_prompt,
                #continue_conversation=continue_conversation,
                resume = session_id,
                max_turns=100,
                cwd=cwd_dir,
                permission_mode = "bypassPermissions",
                #mcp_servers=self.mcp_servers,
                max_thinking_tokens=0,
                extra_args={
                    #"verbose": None,
                    #"output-format": "stream-json",
                    "permission-mode": "acceptEdits",
                    #"mcp-debug": None,
                    "dangerously-skip-permissions": None
                }
            ))
        # Connect with initial message stream
        async def message_stream():
             yield {"type": "user", "message": {"role": "user", "content": f"{user_req} /nothink"}}

        await client.connect(message_stream())
        #await client.connect()
        result = {
            "is_error":False
        }
        max_content_error_count = 0
        new_session_id = session_id
        while(True):
            result["is_error"] = False
            async for message in client.receive_messages():
                if type(message).__name__ == "ResultMessage":
                    #result:ResultMessage = message
                    result["is_error"] = message.is_error
                    result["message"] = f"{message.result}"
                    new_session_id = message.session_id
                    #if message.is_error:
                    break
                elif type(message).__name__ == "SystemMessage":
                    #progress_callback(0, message.data)
                    progress_callback(0, "SystemMessage")
                    new_session_id = message.data.get("session_id")
                # elif type(message).__name__ == "UserMessage":
                #     progress_callback(0, message.content)
                # elif type(message).__name__ == "AssistantMessage":
                #     progress_callback(0, message.content)
                else: #UserMessage or AssistantMessage
                    if hasattr(message, 'content'):
                        # Print streaming content as it arrives
                        for block in message.content:
                            if type(block).__name__ == "TextBlock":
                                progress_callback(0, "TextBlock", block.text)
                                if "maximum context length" in block.text:
                                    #print("!!!ERROR!!!:", block.text)
                                    result["message"] = block.text
                                    result["is_error"] = True
                                    break
                            elif type(block).__name__ == "ThinkingBlock":
                                progress_callback(0, "ThinkingBlock", block.content)
                            elif type(block).__name__ == "ToolUseBlock":
                                progress_callback(0, "ToolUseBlock", block.name)
                            elif type(block).__name__ == "ToolResultBlock":
                                progress_callback(0, "ToolResultBlock", block.content)
                            else:
                                print("\n")
                
                if result["is_error"]:
                    break
        
            if not result["is_error"]:
                break
            # 如果有错误，检查是否超过上下文的错误
            if "maximum context" in result["message"] or "too large" in result["message"]:
                if max_content_error_count > 2:
                    print("ERROR: Too many maximum context errors, clear session and try again")
                    await self.run_command(client, "/clear", session_id)
                if max_content_error_count > 4:
                    print("ERROR: Too many maximum context errors, exit.")
                    break
                
                max_content_error_count += 1
                #session_id = new_session_id
                # 执行压缩命令后继续
                await self.run_command(client, "/compact", new_session_id)
                
                await client.query("继续执行任务", session_id)
                continue
            else:
                #其他错误，退出循环
                break
                
        
        await client.disconnect()
        
        if result["is_error"]:
            print(f"ERROR: {result}")
        result["success"] = not result["is_error"]
        result["session_id"] = new_session_id
        return result
    
    async def run_command(self, client:ClaudeSDKClient, command: str, session_id: str = None):         
        await client.query(command, session_id)
        async for message in client.receive_messages():
                if type(message).__name__ == "ResultMessage":
                    print(f"Command Result : {message.result}")
                    #if message.is_error:
                    break
        # 执行shell交互式命令，启动: claude -r session_id
        # 等待终端的最后出现 > 提示符后输入: /command
        # 然后等待命令结束, 输出结果: > /command
        # 退出shell
        return
    # 压缩会话
    async def compact_session(self, client:ClaudeSDKClient, session_id: str = None):
        
        return
                       
            
    def remove_think(self, text):
        """
        移除字符串中所有<ai>和</ai>标签及其之间的内容。

        参数:
            text (str): 输入的字符串

        返回:
            str: 处理后的字符串
        """
        pattern = r'<think>.*?</think>'
        while True:
            new_text = re.sub(pattern, '', text, flags=re.DOTALL)
            if new_text == text:
                break
            text = new_text
        if all(c in ('\n', ' ') for c in text):
            text = text.replace('\n', '').replace(' ', '')
        return text    