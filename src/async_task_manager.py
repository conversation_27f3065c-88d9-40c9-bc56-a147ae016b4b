#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import uuid
import time
import json
import threading
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Callable, Any
from concurrent.futures import ThreadPoolExecutor, Future
import traceback

# 添加StorageManager导入
from .storage import StorageManager
from .config import Config

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"      # 等待中
    RUNNING = "running"      # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"        # 失败
    CANCELLED = "cancelled"  # 已取消

class AsyncTask:
    """异步任务类"""
    
    def __init__(self, task_id: str, task_type: str, task_name: str, 
                 func: Callable = None, args: tuple = (), kwargs: dict = None):
        self.task_id = task_id
        self.task_type = task_type
        self.task_name = task_name
        self.func = func
        self.args = args
        self.kwargs = kwargs or {}
        
        self.status = TaskStatus.PENDING
        self.created_at = datetime.now()
        self.started_at = None
        self.completed_at = None
        self.progress = 0
        self.result = None
        self.error = None
        self.error_traceback = None
        
        # 进度回调函数
        self.progress_callback = None
        
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'task_name': self.task_name,
            'status': self.status.value,
            'created_at': self.created_at.isoformat() if isinstance(self.created_at, datetime) else self.created_at,
            'started_at': self.started_at.isoformat() if isinstance(self.started_at, datetime) else self.started_at,
            'completed_at': self.completed_at.isoformat() if isinstance(self.completed_at, datetime) else self.completed_at,
            'progress': self.progress,
            'result': self.result,
            'error': self.error
        }
    
    @classmethod
    def from_dict(cls, data: dict):
        """从字典创建任务对象"""
        task = cls(
            task_id=data['task_id'],
            task_type=data['task_type'],
            task_name=data['task_name']
        )
        task.status = TaskStatus(data['status'])
        task.created_at = datetime.fromisoformat(data['created_at']) if data['created_at'] else None
        task.started_at = datetime.fromisoformat(data['started_at']) if data['started_at'] else None
        task.completed_at = datetime.fromisoformat(data['completed_at']) if data['completed_at'] else None
        task.progress = data['progress']
        task.result = data['result']
        task.error = data['error']
        return task
        
    def to_dict(self) -> dict:
        """转换为字典"""
        return {
            'task_id': self.task_id,
            'task_type': self.task_type,
            'task_name': self.task_name,
            'status': self.status.value,
            'created_at': self.created_at.isoformat() if isinstance(self.created_at, datetime) else self.created_at,
            'started_at': self.started_at.isoformat() if isinstance(self.started_at, datetime) else self.started_at,
            'completed_at': self.completed_at.isoformat() if isinstance(self.completed_at, datetime) else self.completed_at,
            'progress': self.progress,
            'result': self.result,
            'error': self.error
        }

class AsyncTaskManager:
    """异步任务管理器"""
    
    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.tasks: Dict[str, AsyncTask] = {}
        self.futures: Dict[str, Future] = {}
        self.lock = threading.Lock()
        
        # 初始化存储管理器
        config = Config()
        self.storage = StorageManager(config)
        
        # 从存储中加载任务
        self._load_tasks_from_storage()
        
    def _load_tasks_from_storage(self):
        """从存储中加载任务"""
        try:
            tasks_data = self.storage.load_tasks()
            for task_data in tasks_data:
                # 加载任务                
                task = AsyncTask.from_dict(task_data)
                self.tasks[task.task_id] = task
        except Exception as e:
            print(f"加载任务时出错: {e}")
    
    def _save_tasks_to_storage(self, task: AsyncTask):
        """将任务保存到存储"""
        try:
            tasks_data = task.to_dict()
            self.storage.save_task(tasks_data)
        except Exception as e:
            print(f"保存任务时出错: {e}")
    
    def submit_task(self, task_type: str, task_name: str, func: Callable, 
                   args: tuple = (), kwargs: dict = None) -> str:
        """提交异步任务"""
        task_id = str(uuid.uuid4())
        
        with self.lock:
            # 创建任务对象
            task = AsyncTask(task_id, task_type, task_name, func, args, kwargs)
            self.tasks[task_id] = task
            
            # 提交到线程池
            future = self.executor.submit(self._execute_task, task)
            self.futures[task_id] = future
            
            # 保存任务到存储
            self._save_tasks_to_storage(task)
            
        return task_id
    
    def _execute_task(self, task: AsyncTask):
        """执行任务"""
        try:
            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.started_at = datetime.now()
            
            # 设置进度回调
            def update_progress(progress: int, message: str=''):
                task.progress = min(100, max(0, progress))
                # 保存任务状态更新
                with self.lock:
                    self._save_tasks_to_storage(task)
            
            task.progress_callback = update_progress
            
            # 执行任务函数
            if task.func:
                if 'progress_callback' in task.func.__code__.co_varnames:
                    # 如果函数支持进度回调，传入回调函数
                    result = task.func(*task.args, progress_callback=update_progress, **task.kwargs)
                else:
                    result = task.func(*task.args, **task.kwargs)
                
                # 任务完成
                if result.get("errors"):
                    task.error = result.get("errors")
                    task.status = TaskStatus.FAILED
                else:
                    task.status = TaskStatus.COMPLETED
                    # 保存结果到文件
                    result_file = self.storage.save_task_result(task.task_id, result)
                    if result_file:
                        task.result = {'result_file': result_file, 'summary': result}
                    else:
                        task.result = result
                task.completed_at = datetime.now()
                task.progress = 100
            else:
                # 没有函数可执行，标记为完成
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()
                task.progress = 100
            
            # 保存任务状态更新
            with self.lock:
                self._save_tasks_to_storage(task)
            
            return task.result
            
        except Exception as e:
            # 任务失败
            task.status = TaskStatus.FAILED
            task.completed_at = datetime.now()
            task.error = str(e)
            task.error_traceback = traceback.format_exc()
            
            # 保存任务状态更新
            with self.lock:
                self._save_tasks_to_storage(task)
            
            raise e
    
    def get_task_status(self, task_id: str) -> Optional[dict]:
        """获取任务状态"""
        with self.lock:
            task = self.tasks.get(task_id)
            if task:
                return task.to_dict()
            return None
    
    def get_task_result(self, task_id: str) -> Any:
        """获取任务结果"""
        with self.lock:
            task = self.tasks.get(task_id)
            if task and task.status == TaskStatus.COMPLETED:
                result = task.result
                # 如果结果保存在文件中，从文件加载
                if isinstance(result, dict) and 'result_file' in result:
                    file_result = self.storage.load_task_result(result['result_file'])
                    if file_result:
                        return file_result
                    else:
                        # 文件不存在，返回摘要
                        return result.get('summary', result)
                return result
            return None
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.lock:
            future = self.futures.get(task_id)
            task = self.tasks.get(task_id)
            
            if future and task:
                if task.status == TaskStatus.PENDING:
                    # 如果任务还未开始，直接取消
                    cancelled = future.cancel()
                    if cancelled:
                        task.status = TaskStatus.CANCELLED
                        task.completed_at = datetime.now()
                    # 保存任务状态更新
                    self._save_tasks_to_storage(task)
                    return cancelled
                elif task.status == TaskStatus.RUNNING:
                    # 如果任务正在运行，标记为取消（实际取消需要任务函数配合）
                    task.status = TaskStatus.CANCELLED
                    task.completed_at = datetime.now()
                    # 保存任务状态更新
                    self._save_tasks_to_storage(task)
                    return True
            
            return False
    
    def list_tasks(self, task_type: str = None, status: TaskStatus = None) -> List[dict]:
        """列出任务"""
        with self.lock:
            tasks = []
            for task in self.tasks.values():
                if task_type and task.task_type != task_type:
                    continue
                if status and task.status != status:
                    continue
                tasks.append(task.to_dict())
            
            # 按创建时间倒序排列
            tasks.sort(key=lambda x: x['created_at'], reverse=True)
            return tasks
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的任务"""
        with self.lock:
            current_time = datetime.now()
            tasks_to_remove = []

            for task_id, task in self.tasks.items():
                if task.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    if task.completed_at:
                        age_hours = (current_time - task.completed_at).total_seconds() / 3600
                        if age_hours > max_age_hours:
                            tasks_to_remove.append(task_id)

            for task_id in tasks_to_remove:
                del self.tasks[task_id]
                if task_id in self.futures:
                    del self.futures[task_id]

            # 清理过期的结果文件
            self.storage.cleanup_expired_results(max_age_hours)
    
    def shutdown(self, wait: bool = True):
        """关闭任务管理器"""
        # with self.lock:
        #     self._save_tasks_to_storage()
        self.executor.shutdown(wait=wait)

# 全局任务管理器实例
_task_manager = None

def get_task_manager() -> AsyncTaskManager:
    """获取全局任务管理器实例"""
    global _task_manager
    if _task_manager is None:
        _task_manager = AsyncTaskManager()
    return _task_manager