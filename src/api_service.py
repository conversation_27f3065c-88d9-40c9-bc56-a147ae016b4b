#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
REST API服务：根据需求生成测试用例
提供独立的API端点供外部系统调用
"""

from flask import Flask, request, jsonify
from typing import List, Dict, Any
import json
import uuid
from datetime import datetime

from ..src.config import Config
from ..src.storage import StorageManager
from ..src.test_manager import TestManager
from ..src.requirement_manager import RequirementManager
from ..src.knowledge_manager import KnowledgeManager
from ..src.utils.llm_client import LLMClient


class TestCaseGenerationAPI:
    """测试用例生成API服务"""
    
    def __init__(self):
        self.config = Config()
        self.storage = StorageManager(self.config)
        self.knowledge_manager = KnowledgeManager(self.storage, self.config)
        self.llm_client = LLMClient(self.config)
        self.test_manager = TestManager(self.storage, self.config)
        self.requirement_manager = RequirementManager(self.storage, self.config)
    
    def create_app(self):
        """创建Flask应用"""
        app = Flask(__name__)
        
        @app.route('/aitest/v1/generate_test_cases', methods=['POST'])
        def generate_test_cases():
            """根据需求生成测试用例的API端点"""
            try:
                # 获取请求数据 - 现在是需求数组格式
                requirements_data = request.get_json()
                if not requirements_data:
                    return jsonify({
                        'success': False,
                        'errors': '请求数据不能为空',
                        'test_cases': []
                    }), 400

                if not isinstance(requirements_data, list):
                    return jsonify({
                        'success': False,
                        'errors': '请求数据应为需求数组格式',
                        'test_cases': []
                    }), 400

                if len(requirements_data) == 0:
                    return jsonify({
                        'success': False,
                        'errors': '需求数组不能为空',
                        'test_cases': []
                    }), 400

                # 验证需求数据格式并提取project_id和test_types
                project_id = None
                test_types = ['functional']  # 默认值

                for req in requirements_data:
                    if not isinstance(req, dict):
                        return jsonify({
                            'success': False,
                            'errors': '需求数据格式错误，应为对象数组',
                            'test_cases': []
                        }), 400

                    # 从第一个需求中获取project_id和test_types
                    if project_id is None:
                        project_id = req.get('project_id')
                        test_types = req.get('test_types', ['functional'])

                    # 验证必填字段
                    required_fields = ['project_id', 'number', 'title', 'spec']
                    for field in required_fields:
                        if not req.get(field):
                            return jsonify({
                                'success': False,
                                'errors': f'需求中缺少必填字段: {field}',
                                'test_cases': []
                            }), 400
                
                # 转换需求数据格式，添加必要字段
                processed_requirements = []
                for req in requirements_data:
                    processed_req = {
                        'id': str(uuid.uuid4()),
                        'project_id': req.get('project_id'),
                        'number': req.get('number', ''),
                        'title': req.get('title', ''),
                        'spec': req.get('spec', ''),
                        'businessProcess': req.get('businessProcess', ''),
                        'constraints': req.get('constraints', ''),
                        'verify': req.get('verify', ''),
                        'comment': req.get('comment', ''),
                        'priority': req.get('priority', '中'),
                        'type': req.get('type', '功能需求'),
                        'status': '待处理',
                        'created_at': datetime.now().isoformat(),
                        'updated_at': datetime.now().isoformat(),
                        'ai_generated': False
                    }
                    processed_requirements.append(processed_req)

                # 使用默认值
                use_knowledge_base = True
                max_threads = 4
                
                # 调用测试管理器生成测试用例
                result = self.test_manager.generate_test_cases_from_requirements(
                    project_id=project_id,
                    requirements=processed_requirements,
                    test_types=test_types,
                    use_knowledge_base=use_knowledge_base,
                    max_threads=max_threads
                )
                
                if result['success']:
                    # 转换输出格式，符合新的响应格式
                    formatted_test_cases = []
                    for test_case in result['test_cases']:
                        formatted_case = {
                            'name': test_case.get('name', ''),
                            'precondition': test_case.get('precondition', ''),
                            'stepsJson': test_case.get('stepsJson', ''),
                            'expectsJson': test_case.get('expectsJson', ''),
                            'keywords': test_case.get('keywords', ''),
                            'pri': test_case.get('pri', '2'),
                            'type': test_case.get('type', 'functional'),
                            'auto': test_case.get('auto', 'no'),
                            'distinguish': test_case.get('distinguish', '0'),
                            'executionHours': test_case.get('executionHours', '1'),
                            'stage': test_case.get('stage', '功能测试阶段'),
                            'req_number': test_case.get('req_number', ''),
                            'project_id': project_id
                        }
                        formatted_test_cases.append(formatted_case)

                    return jsonify({
                        'success': True,
                        'errors': '',
                        'test_cases': formatted_test_cases
                    })
                else:
                    return jsonify({
                        'success': False,
                        'errors': result.get('error', '生成测试用例失败'),
                        'test_cases': []
                    }), 500
                    
            except Exception as e:
                return jsonify({
                    'success': False,
                    'errors': f'服务器内部错误: {str(e)}',
                    'test_cases': []
                }), 500
        
        @app.route('/aitest/v1/health', methods=['GET'])
        def health_check():
            """健康检查端点"""
            return jsonify({
                'status': 'healthy',
                'service': 'test-case-generation-api',
                'timestamp': datetime.now().isoformat()
            })
        
        return app


def create_api_app():
    """创建API应用的工厂函数"""
    api_service = TestCaseGenerationAPI()
    return api_service.create_app()


if __name__ == '__main__':
    # 独立运行API服务
    app = create_api_app()
    app.run(host='0.0.0.0', port=5001, debug=True)
