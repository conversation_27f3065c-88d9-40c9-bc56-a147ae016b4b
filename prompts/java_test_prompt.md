- 分析用户提供的接口定义，根据接口定义生成java testng测试用例和各种测试数据。
- 每个接口生成一个java测试程序和相应的测试数据
- 生成的java测试代码写入src/test/java目录下, 包名统一为: com.sansec.hsm.SVSCrypto
- 生成的测试数据写到testCase目录下的csv文件
- ***确保为所有接口生成测试用例***
- 以下是一个测试用例的示例文件，仅用于说明测试用例的结构和测试方法:
```java
package com.sansec.hsm.SVSCrypto;
import 需要引入的包名;

public class SVS_generateBarCode39Test extends BaseTestSuite {

    private static final Logger log = LogManager.getLogger(SVS_generateBarCode39Test.class);
    private static final String SHEET_NAME = "GenerateBarCode39";
    private static final String FILE_PATH = "testCase/SVS_V10_BarCode.xls";
    public static String casemark = "ID";
    /**
     * 获取用例
     * @return
     * @throws BiffException
     * @throws IOException
     */
    @DataProvider(name = SHEET_NAME)
    public static Object[][] getExcelData() throws BiffException, IOException {
        ExcelData e = new ExcelData(SHEET_NAME);
        return e.getExcelData(FILE_PATH,casemark,"");
    }

    /**
     * 1.11.1.生成39条码
     * @param data
     * @throws Exception
     */
    @Test(dataProvider = SHEET_NAME)
    public void test_GenerateBarCode39(HashMap<String, String> data) {
        String sdkname="1.11.1.生成39条码";
        BarCodeParameters barCodeParameters = new BarCodeParameters();
        String msg = null;
        boolean addCheckSUM = false;
        float wideFactor = 0;
        boolean displayStartStop = false;
        String ID = null;
        boolean exceptResult = false;
        byte[] BarCode39 = null;
        boolean pic_result = false;


        try {
            ID = InUtil.isnullToString(data.get("ID"));
            exceptResult = Boolean.parseBoolean(data.get("expect_result"));

            //条码参数
            barCodeParameters.setBarHeight(Integer.parseInt(data.get("height")));
            barCodeParameters.setDoQuietZone(Boolean.parseBoolean(data.get("doQuietZone")));
            barCodeParameters.setQuietZoneWidth(Integer.parseInt(data.get("quietZoneWidth")));
            barCodeParameters.setDisplayHumanReadable(Boolean.parseBoolean(data.get("displayHumanReadable")));
            barCodeParameters.setImageFormat(Integer.parseInt(data.get("imageFormat")));
            barCodeParameters.setModuleWidth(Integer.parseInt(data.get("moduleWidth")));

            msg = InUtil.isnullToString(data.get("msg"));

            addCheckSUM = Boolean.parseBoolean(data.get("addCheckSUM"));
            wideFactor = Float.parseFloat(data.get("wideFactor"));
            displayStartStop = Boolean.parseBoolean(data.get("displayStartStop"));

            // 被测试的接口
            BarCode39 = crypto.saf_GenerateBarCode39(msg,barCodeParameters, addCheckSUM, wideFactor, displayStartStop);


            Assert.assertEquals(BarCode39!=null, exceptResult);
            log.info("第" + ID + "条用例，"+ sdkname +"运算成功。返回值：" + Arrays.toString(BarCode39));


            data.clear();

        } catch (Exception e) {
            if (false != exceptResult) {
                log.error("第" + ID + "条用例，"+ sdkname +"运算失败。异常描述：" + e.getMessage());

            } else {
                log.info("第" + ID + "条异常用例，期望结果：" + exceptResult + "与实际结果一致。" + e.getMessage());
            }
            Assert.assertEquals(false, exceptResult);

        }
        data.clear();
    }


}
```