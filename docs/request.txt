---------------------------------------------1-------------------------------------------------------------------------------

[TestCaseForStar(project_id=73, number=712, title=主机服务的客户端IP访问控制, spec=支持IPv4/IPv6两种协议；支持配置IP段方式；支持配置黑白名单两种模式限制（目前白名单都已实现，未实现黑名单）；名单数目：0-256；支持多分区模式。, businessProcess=, constraints=, verify=操作入口：主菜单“主机服务管理”=>客户端IP名单配置，进入操作页面。支持的功能：显示当前配置：IP名单类型（黑、白名单）、IP名单（地址/段）列表；修改名单类型：黑名单、白名单；添加IP名单：点击“添加”按钮，弹框输入要添加的客户端IP地址或段；支持单个IP、多IP、IP段形式的输入；删除，删除选定的一个或多个IP段；实时生效，已建立的连接被自动断开；【多分区】设置配置同步源：选择X分区；若选中，本分区不允许执行修改/添加/删除操作；若同步源被删除，则自动解除同步关联；导出配置到文件；从配置文件中导入；修改、添加、删除操作立即生效，无需重启服务；, comment=, test_types=[functional])]

---------------------------------------------2-------------------------------------------------------------------------------

[TestCaseForStar(project_id=73, number=711, title=业务服务属性配置, spec=, businessProcess=, constraints=, verify=操作入口：主菜单“服务管理”=>业务服务，进入业务服务配置页。进入配置页后，选择分区，显示出当前的分区内的各项配置，每项都可修改；服务端口号，1-65535；出厂默认为8000会话超时时间，0-65535分钟；出厂默认为30最大连接数，1-10000（上限为出厂设置）；出厂默认为1024；【金融】报文编码格式，下拉框，ASCII、EBCDIC；出厂默认为ASCII【金融】明文PIN长度，4-12；出厂默认为6；详见禅道需求#41；【金融】消息头长度，0-255；出厂默认为0；点击“保存”，若需改端口号则提示：需重启业务服务生效，询问是否立即重启；页面支持的操作包括：, comment=, test_types=[functional])]