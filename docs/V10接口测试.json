{"文档信息": {"文档名称": "V10签名验签服务器接口设计V0.6", "文档格式": "Word文档", "解析时间": "2025-09-07 15:08:41", "文档路径": "docs/V10接口测试.docx", "表格数量": 8}, "统计信息": {"总接口数": 8, "分类统计": {"SDK初始化": 2, "证书管理": 5, "其他": 1}, "算法统计": {}, "参数统计": {"总参数数": 7, "有参数的接口数": 6, "平均参数数": 0.88}}, "分类接口": {"SDK初始化": [{"章节": "SDK初始化", "需求编号": "PR-F-SVS-0001", "需求名称": "", "优先级": "", "接口功能": "通过传入配置文件或配置字符串方式初始化签名验签服务器客户端实例", "接口定义": "public static synchronized SVSClient getInstance(String config) throws SVSException public static synchronized SVSClient getInstance(String config,String clientPfxPwd) throws SVSException", "输入参数": [{"参数名": "config", "类型": "String", "必填": "是", "说明": "配置文件绝对路径或配置内容字符串"}], "输出": "调用接口的实例对象", "异常": "SVSException", "其它说明": "单实例方式初始化，避免重复初始化； 2、配置格式说明： ①注释行以 符号“#” 起始，不支持行内注释 ②配置域以方括号“[”和“]”标识 ③配置项格式为 “键名（Key） = 键值（Value）” ④配置域与键名不区分大小写，但为了直观建议配置域名使用大写 ⑤支持使用空白字符（空格或制表符）等对内容进行对齐操作 ⑥可在接口内拼装字符串传递配置，使用“{”和“}”包括所有内容，使用“;”表示换行", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0001", "需求名称": "", "优先级": "", "接口功能": "通过传入配置对象信息方式初始化签名验签服务器客户端实例", "接口定义": "public static synchronized SVSClient getInstance(List<HsmInfo> hsmInfoList,PoolInfo poolInfo) throws SVSException", "输入参数": [{"参数名": "hsmInfoList", "类型": "List<HsmInfo>", "必填": "是", "说明": "加密机设备信息"}, {"参数名": "poolInfo", "类型": "PoolInfo", "必填": "是", "说明": "公共配置信息，包括日志/TLS配置/全局配置/应用认证域"}], "输出": "调用接口的实例对象", "异常": "SVSException", "其它说明": "1、单实例方式初始化，避免重复初始化； 2、HsmInfo对象属性信息，对应加密机域，多个设备通过集合方式传入初始化接口 3、PoolInfo对象属性信息 ，包括日志/TLS配置/全局配置", "支持算法": []}], "证书管理": [{"章节": "证书管理", "需求编号": "PR-F-SVS-0007", "需求名称": "", "优先级": "", "接口功能": "返回所有信任域证书", "接口定义": "public List<TrustUserCertInfo> queryTrustCertList() throws SVSException", "输入参数": [], "输出": "无", "异常": "SVSException", "其它说明": "返回所有信任域证书列表，一般该列表不大", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0008", "需求名称": "", "优先级": "", "接口功能": "根据名称查询信任域证书", "接口定义": "public TrustUserCertInfo queryTrustCertByName(String name) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "证书名称"}], "输出": "TrustUserCertInfo（1.7.1章节）：可能为空，表示没有指定的证书", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0009", "需求名称": "", "优先级": "", "接口功能": "根据证书序列号查询信任域证书", "接口定义": "public TrustUserCertInfo queryTrustCertBySN(String serialNumber) throws SVSException", "输入参数": [{"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}], "输出": "TrustUserCertInfo（1.7.1章节）：可能为空，表示没有指定的证书", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0010", "需求名称": "", "优先级": "", "接口功能": "根据证书主题查询信任域证书", "接口定义": "public TrustUserCertInfo queryTrustCertBySubjectName(String subject) throws SVSException", "输入参数": [{"参数名": "subject", "类型": "String", "必填": "是", "说明": "证书主题"}], "输出": "TrustUserCertInfo（1.7.1章节）：可能为空，表示没有指定的证书", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0011", "需求名称": "", "优先级": "", "接口功能": "新增信任域证书（证书公钥算法支持：RSA、SM2、ECSDA、EdDSA）", "接口定义": "public void addTrustCert(String name，String certificate) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称（信任域范围内唯一）"}], "输出": "无", "异常": "SVSException", "其它说明": "信任域证书不允许重复导入，名称唯一，格式正确，当前处于有效期内", "支持算法": []}], "密钥管理": [], "密码运算": [], "非对称算法": [], "消息签名验签": [], "PKCS7操作": [], "数字信封": [], "XML操作": [], "PDF操作": [], "OFD操作": [], "条形码": [], "时间戳": [], "电子签章": [], "监控": [], "工具类": [], "其他": [{"章节": "其他操作", "需求编号": "PR-F-SVS-0006", "需求名称": "", "优先级": "", "接口功能": "断开连接，释放资源。", "接口定义": "public synchronized void finalize() throws SVSException", "输入参数": [], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}]}, "所有接口": [{"章节": "SDK初始化", "需求编号": "PR-F-SVS-0001", "需求名称": "", "优先级": "", "接口功能": "通过传入配置文件或配置字符串方式初始化签名验签服务器客户端实例", "接口定义": "public static synchronized SVSClient getInstance(String config) throws SVSException public static synchronized SVSClient getInstance(String config,String clientPfxPwd) throws SVSException", "输入参数": [{"参数名": "config", "类型": "String", "必填": "是", "说明": "配置文件绝对路径或配置内容字符串"}], "输出": "调用接口的实例对象", "异常": "SVSException", "其它说明": "单实例方式初始化，避免重复初始化； 2、配置格式说明： ①注释行以 符号“#” 起始，不支持行内注释 ②配置域以方括号“[”和“]”标识 ③配置项格式为 “键名（Key） = 键值（Value）” ④配置域与键名不区分大小写，但为了直观建议配置域名使用大写 ⑤支持使用空白字符（空格或制表符）等对内容进行对齐操作 ⑥可在接口内拼装字符串传递配置，使用“{”和“}”包括所有内容，使用“;”表示换行", "支持算法": []}, {"章节": "SDK初始化", "需求编号": "PR-F-SVS-0001", "需求名称": "", "优先级": "", "接口功能": "通过传入配置对象信息方式初始化签名验签服务器客户端实例", "接口定义": "public static synchronized SVSClient getInstance(List<HsmInfo> hsmInfoList,PoolInfo poolInfo) throws SVSException", "输入参数": [{"参数名": "hsmInfoList", "类型": "List<HsmInfo>", "必填": "是", "说明": "加密机设备信息"}, {"参数名": "poolInfo", "类型": "PoolInfo", "必填": "是", "说明": "公共配置信息，包括日志/TLS配置/全局配置/应用认证域"}], "输出": "调用接口的实例对象", "异常": "SVSException", "其它说明": "1、单实例方式初始化，避免重复初始化； 2、HsmInfo对象属性信息，对应加密机域，多个设备通过集合方式传入初始化接口 3、PoolInfo对象属性信息 ，包括日志/TLS配置/全局配置", "支持算法": []}, {"章节": "其他操作", "需求编号": "PR-F-SVS-0006", "需求名称": "", "优先级": "", "接口功能": "断开连接，释放资源。", "接口定义": "public synchronized void finalize() throws SVSException", "输入参数": [], "输出": "无", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0007", "需求名称": "", "优先级": "", "接口功能": "返回所有信任域证书", "接口定义": "public List<TrustUserCertInfo> queryTrustCertList() throws SVSException", "输入参数": [], "输出": "无", "异常": "SVSException", "其它说明": "返回所有信任域证书列表，一般该列表不大", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0008", "需求名称": "", "优先级": "", "接口功能": "根据名称查询信任域证书", "接口定义": "public TrustUserCertInfo queryTrustCertByName(String name) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "证书名称"}], "输出": "TrustUserCertInfo（1.7.1章节）：可能为空，表示没有指定的证书", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0009", "需求名称": "", "优先级": "", "接口功能": "根据证书序列号查询信任域证书", "接口定义": "public TrustUserCertInfo queryTrustCertBySN(String serialNumber) throws SVSException", "输入参数": [{"参数名": "serialNumber", "类型": "String", "必填": "是", "说明": "证书序列号"}], "输出": "TrustUserCertInfo（1.7.1章节）：可能为空，表示没有指定的证书", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0010", "需求名称": "", "优先级": "", "接口功能": "根据证书主题查询信任域证书", "接口定义": "public TrustUserCertInfo queryTrustCertBySubjectName(String subject) throws SVSException", "输入参数": [{"参数名": "subject", "类型": "String", "必填": "是", "说明": "证书主题"}], "输出": "TrustUserCertInfo（1.7.1章节）：可能为空，表示没有指定的证书", "异常": "SVSException", "其它说明": "", "支持算法": []}, {"章节": "证书管理", "需求编号": "PR-F-SVS-0011", "需求名称": "", "优先级": "", "接口功能": "新增信任域证书（证书公钥算法支持：RSA、SM2、ECSDA、EdDSA）", "接口定义": "public void addTrustCert(String name，String certificate) throws SVSException", "输入参数": [{"参数名": "name", "类型": "String", "必填": "是", "说明": "名称（信任域范围内唯一）"}], "输出": "无", "异常": "SVSException", "其它说明": "信任域证书不允许重复导入，名称唯一，格式正确，当前处于有效期内", "支持算法": []}]}