# AI/测试平台

## POST 生成测试用例

POST https://**********/aitest/api/generate_test_cases

> Body 请求参数

```json
[
  {
    "project_id": "15ac396f-7ee0-4d39-9f5c-d58582b6b4ef",
    "number": "PR-F-1001",
    "title": "监控组件安装适配",
    "spec": "需要在AI一体机上安装监控组件以及所需的基础运行环境，保证监控组件在AI一体机上稳定运行，并提供可监控指标的汇总表",
    "businessProcess": "不涉及",
    "constraints": "不涉及",
    "verify": "监控组件安装成功，并可稳定运行",
    "comment": "不涉及",
    "test_types": [
      "functional"
    ]
  }
]
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|Authorization|header|string| 否 |none|
|body|body|object| 否 |none|

> 返回示例

> 200 Response

```json
{
  "errors": "",
  "success": true,
  "test_cases": [
    {
            "auto": "no",
            "description": "验证监控组件在AI一体机上的安装流程是否正常，包括安装界面操作和安装结果验证",
            "distinguish": "0",
            "executionHours": "1",
            "expects": [
                "安装进度条正常显示",
                "安装过程中显示清晰的进度提示",
                "安装完成后显示成功提示",
                "在已安装组件列表中显示监控组件",
                "监控组件状态显示为'已安装'"
            ],
            "keywords": "监控组件,安装流程,界面操作",
            "name": "监控组件安装流程验证",
            "precondition": "AI一体机已准备就绪；监控组件安装包已下载；管理员权限已获取",
            "pri": "1",
            "project_id": "15ac396f-7ee0-4d39-9f5c-d58582b6b4ef",
            "req_number": "PR-F-1001",
            "requirement_ids": [
                ""
            ],
            "stage": "功能界面操作测试阶段",
            "status": "待执行",
            "steps": [
                "1. 打开AI一体机系统管理界面",
                "2. 导航到'组件管理'或'安装管理'页面",
                "3. 点击'添加组件'或'安装新组件'按钮",
                "4. 选择监控组件安装包",
                "5. 点击'安装'按钮开始安装过程",
                "6. 观察安装进度和状态提示",
                "7. 等待安装完成"
            ],
            "type": "functional"
        },
        {
            "auto": "no",
            "description": "验证监控组件所需的基础运行环境是否正确配置，包括依赖项检查和安装",
            "distinguish": "0",
            "executionHours": "0.5",
            "expects": [
                "显示监控组件所需的基础运行环境列表",
                "每个依赖项都有明确的安装状态标识",
                "未安装的依赖项可以正常安装",
                "所有依赖项安装完成后状态显示为'已安装'",
                "保存配置成功",
                "监控组件重启成功"
            ],
            "keywords": "基础环境,配置管理,依赖项",
            "name": "基础运行环境配置验证",
            "precondition": "监控组件已安装；管理员权限已获取",
            "pri": "1",
            "project_id": "15ac396f-7ee0-4d39-9f5c-d58582b6b4ef",
            "req_number": "PR-F-1001",
            "requirement_ids": [
                ""
            ],
            "stage": "功能界面操作测试阶段",
            "status": "待执行",
            "steps": [
                "1. 打开AI一体机系统管理界面",
                "2. 导航到'环境配置'或'系统设置'页面",
                "3. 查看监控组件所需的基础运行环境列表",
                "4. 检查每个依赖项的安装状态",
                "5. 对未安装的依赖项执行安装操作",
                "6. 验证所有依赖项的安装状态",
                "7. 保存配置并重启监控组件"
            ],
            "type": "functional"
        }
  ]
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## 异步POST生成测试用例

POST https://**********/aitest/api/async/generate_test_cases

> Body 请求参数(与同步方法相同)

```json
[
  {
    "project_id": "15ac396f-7ee0-4d39-9f5c-d58582b6b4ef",
    "number": "PR-F-1001",
    "title": "监控组件安装适配",
    "spec": "需要在AI一体机上安装监控组件以及所需的基础运行环境，保证监控组件在AI一体机上稳定运行，并提供可监控指标的汇总表",
    "businessProcess": "不涉及",
    "constraints": "不涉及",
    "verify": "监控组件安装成功，并可稳定运行",
    "comment": "不涉及",
    "test_types": [
      "functional"
    ]
  }
]
```
响应结果：
```json
{
    "message": "任务已创建，请使用task_id查询进度和结果",
    "success": true,
    "task_id": "549ccfeb-0254-4583-9a15-3baeb88531c4"
}
```

获取异步任务结果：
GET https://**********/aitest/api/tasks/result/:task_id
如：https://**********/aitest/api/tasks/result/549ccfeb-0254-4583-9a15-3baeb88531c4

执行过程中的响应结果:
```json
{
    "progress": 5,
    "status": "running",
    "success": true
}
```

执行完毕的响应结果:
```json
{
    "progress": 100,
    "status": "completed",
    "success": true,
    "test_cases": [
        {
            "auto": "no",
            "description": "验证监控组件在AI一体机上的安装流程是否正常，包括安装界面操作和安装结果验证",
            "distinguish": "0",
            "executionHours": "1",
            "expects": [
                "安装进度条正常显示",
                "安装过程中显示清晰的进度提示",
                "安装完成后显示成功提示",
                "在已安装组件列表中显示监控组件",
                "监控组件状态显示为'已安装'"
            ],
            "keywords": "监控组件,安装流程,界面操作",
            "name": "监控组件安装流程验证",
            "precondition": "AI一体机已准备就绪；监控组件安装包已下载；管理员权限已获取",
            "pri": "1",
            "project_id": "15ac396f-7ee0-4d39-9f5c-d58582b6b4ef",
            "req_number": "PR-F-1001",
            "requirement_ids": [
                ""
            ],
            "stage": "功能界面操作测试阶段",
            "status": "待执行",
            "steps": [
                "1. 打开AI一体机系统管理界面",
                "2. 导航到'组件管理'或'安装管理'页面",
                "3. 点击'添加组件'或'安装新组件'按钮",
                "4. 选择监控组件安装包",
                "5. 点击'安装'按钮开始安装过程",
                "6. 观察安装进度和状态提示",
                "7. 等待安装完成"
            ],
            "type": "functional"
        }
    ]
}
```