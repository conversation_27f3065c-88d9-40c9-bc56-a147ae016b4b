# 实现一个AI智能测试平台
主要功能包括：
## 1.项目管理
- 实现项目的创建、修改、删除、查询。

## 2.需求管理
每个项目有独立的需求管理。
### 2.1 文档管理
- 可上传与项目相关的需求文档、设计文档
- 文档支持word、pdf、markdown格式
- word/pdf格式都需要转换为markdown格式
- 文档支持在线预览
### 2.2 需求功能点管理
- 使用大模型根据需求文档提取需求功能点，生成需求功能列表
- 可根据配置按照需求文档的章节（如markdown的###分隔）批次调用大模型，避免超出大模型上下文长度。
- 支持多线程同时处理多个章节，线程数在配置文件中配置。
- 可以过滤文档章节的目录部分，不参与需求功能点提取。
- 需求功能包括：编号、对应原文档的章节、类型（功能需求、性能需求、安全需求等）、描述、优先级、状态（待处理、处理中、已完成）、创建时间、更新时间等
- 提取的需求功能点支持在线增删改查、分页列表展示、搜索, 支持批量删除，列表页的行数可以在前端下拉框选中10，50，100。
- 功能点列表页面，默认按照编号排序。点击需求编号和标题，可跳转到编辑页面，鼠标移动到标题，可浮动显示详细的需求描述。
- 功能点提取的系统提示词使用文件存储

## 3.知识库管理
- 每个项目有独立的知识库
- 可上传项目相关的知识库文件，如配置文件、数据库脚本、测试用例、用户手册
- 上传的需求/设计文档，自动导入到知识库
- 知识库可使用chroma向量库存储，调用openai兼容的向量模型
- web界面可知识库的检索，测试知识库的效果。
- 知识库的文档切块可以按照markdown的###分隔符(可在配置文件中配置)进行切块

## 4.测试管理
- 每个项目有独立的测试用例管理
- 测试用例包括：用户测试用例和自动化测试用例，其中：用户测试用例是需要有人员执行的，自动化测试用例则是由midscene自动执行的。
- 可批量选择需求功能点，调用大模型生成用户测试用例,生成测试用例时可搜索知识库内容做为参考。
- 可批量选择用户测试用例，调用大模型生成midscene自动化测试用例
- 测试用例支持在线增删改查、分页列表展示、搜索，可以手动添加，支持批量删除，列表页的行数可以在前端下拉框选中10，50，100
- 测试用例与需求功能点要建立关联关系，可以在列表界面点击关联的需求，弹出需求功能点详细信息。
- 生成测试用例的系统提示词用文件存储
- 大模型自动生成的需求功能点、用户测试用例、自动化测试用例，都增加一个修改时间，标记这个数据是否在大模型生成后修改过，并在首页增加统计：可在总数后加括号显示，如：总数（大模型生成数量/修改数量）。
- 根据功能点生成测试用例时可以选择生成哪些测试用例类型（可多选），如：UI测试、接口测试、系统测试、性能测试、安全测试。
   - UI测试：通过界面验证功能的操作步骤、预期结果等。
   - 接口测试：通过API验证功能的操作步骤、预期结果等。
   - 系统测试：执行系统命令，验证功能点是否正常。
   - 性能测试：根据功能点生成性能测试用例，如：并发数、请求数、响应时间等。
   - 安全测试：根据功能点生成安全测试用例，如：数据泄露、越权访问、注入攻击等。
   - 每一类测试使用不同的系统提示词，请优化现有的系统提示词，并增加新的系统提示词，确保每个提示词只能生成特定类型的测试用例。

## 5.系统配置
 - 配置大模型API的：url、apikey、model_name
 - 配置向量模型的：url、apikey、model_name
 - 该项目中用到的系统提示词文件的在线修改
 - 数据存储的路径、上传文件管理的路径、知识库路径，都可以在data目录下分子目录

## 技术实现要求
1. 使用python开发, .py文件在src目录，界面在templates目录下，数据存储在data目录下，配置文件使用.env。
2. WEB界面美观，支持多级菜单，可根据各功能的复杂度拆分出子菜单。菜单在左侧，可收起。
3. 需求、知识库、测试用例的功能都是与一个项目相关的，可以在菜单顶部选择项目，每个功能下就不用再选择项目了。
4. 各功能用不同的html页面实现，不要都在一个页面内。
5. 项目目录结构合理安排
6. 数据存储提供统一的接口，支持json文件存储和sqlite数据库存储，默认为json。
7. 复杂的后台操作，前端界面友好显示处理进度。

## 项目测试
在生成全部功能后，使用以下大模型配置进行测试，并使用docs/目录下的.md文件，验证该项目的所有功能是否正常。
# LLM配置-本地
OPENAI_API_BASE='https://ai.secsign.online:3003/v1'
OPENAI_API_KEY='sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB'
OPENAI_MODEL='qwen3-32b'
# 嵌入模型配置
EMBEDDING_MODEL='embed'
EMBEDDING_API_BASE='https://ai.secsign.online:38080'
EMBEDDING_API_KEY='sk-JpT9PZnwGKDerAIyxmqavr0hEc98aBVnXzLB41fIlCcVVQRB'