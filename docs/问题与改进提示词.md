# 问题1
1.缺失：system/index.html、tests/index.html
2.顶部的“项目列表”下拉框，没有项目展示，刷新后也没有。
3.上传文档后，前端进度弹出框一直转圈，不消失。后端服务调用失败（如400、500错误）时，前端的弹出框也没有销售，并且也不显示具体的错误信息。
4."从文档提取需求"的对话框，在选择文档后，“按章节批次处理”功能，可以再选择章节分隔符，在调用get_document_sections获取章节时用于区分章节。
5.调用大模型提取需求功能点功能，增加多线程并发处理能力，线程数在配置文件中配置。每个线程平均处理文档的多个章节。
6.大模型生成测试用例功能、生成自动化测试用例的功能，也增加多线程处理能力，每个线程平均处理若干个功能点的测试用例。
7.“需求功能点管理”界面，点击“编辑”没有反应；列表界面没有批量删除功能。
8.knowledge_manager.py_split_text的切块功能，可以按照markdown的###分隔符(可在配置文件中配置)进行切块。
9.知识库管理界面的"知识库搜索"，在有结果后,“进度框”没有消失。

# 问题2
解决以下问题，实现中间不用询问，继续完成全部任务：

1.“生成测试用例”对话框“选择需求功能点”的名称都是：undefined
2."测试用例生成失败"，进度框没有关闭。
3.测试用例生成后没有从持久保存，界面的/tests/api/list就没有查询到测试用例。
4."文档上传"成功后，进度框没有关闭，“知识库搜索”出结果后，进度框未关闭；检查下项目的其他功能，进度框可能都有同样问题。
5.大模型根据需求文档抽取的功能点，只有描述，缺失标题, 没有持久化保存。上次修复之前的功能是有保存的。
6.各个列表界面，增加每页行数的选择：10、30、50.
7.知识库移除文档失败: Expected include item to be one of embeddings, documents, metadatas, got ids
8.根据"项目提示词.md"要求的功能，检查项目全部代码是否有遗漏、功能是否完善，不要再有错误。


# 问题3
解决以下问题，实现中间不用询问，继续完成全部任务：

1. 测试用例列表，点击查看、编辑、删除都没有反应
2. 测试用例列表，增加友好：点击名称跳转到详情页面
3. 根据功能点生成的测试用例，管理需求都是：无关联需求

# 问题4
解决以下问题和功能，中间不用询问，继续完成全部任务：

1. "正在删除需求..."对话框，在后台执行完毕后不关闭。
2. "需求功能点管理"界面，点击需求编号和标题，可跳转到编辑页面，鼠标移动到标题，可浮动显示详细的需求描述。
3. "需求功能点管理"列表界面，按照编号排序
4. "测试管理"列表界面，点击“关联需求”，可弹出需求详情页面。
5. "需求功能点管理"列表界面，不需要独立的“全选”框，使用列表标题的复选框做为全选功能，参考：功能点管理 的列表。
6. "测试管理"菜单下加子菜单，把“自动化测试用例”的管理独立出来。
7. "知识库"按照MARKDOWN_SEPARATOR分隔符切块时，还要检查分隔符后是否跟着空格+章节编号，如：### 6.4.2. 监控组件功能扩展 ，这种才是一个章节。使用大模型提取功能点时，也是按照这个规则提取章节进行批次处理。

# 问题5：
解决以下问题和功能，中间不用询问，继续完成全部任务并测试验证：

1. "测试管理"菜单在 点击子菜单后，又缩回菜单了。请参考“需求管理”菜单的实现方式修改。
2. "用户测试用例"列表界面，不需要独立的“全选”框，使用列表标题的复选框做为全选功能，参考：功能点管理 的列表。
3. “自动化测试用例”和“用户测试用例”是两类不同的测试用例，应该使用不同的存储。
4. “自动化测试用例”根据“功能测试”类型的用例生成，输出midscenejs可以执行的格式（参考：https://midscenejs.com/）。
5. “自动化测试用例”支持在线增删改查、分页列表展示、搜索，可以手动添加，支持批量删除.
6. “自动化测试用例”与“用户测试用例”建立关联关系，可以在界面查看“自动化用例”对应的“用户测试用例”
7. 在"生成测试用例"对话框，选择"生成自动化测试用例"时，会先生成“用户测试用例"，再根据"功能测试类型"的用例生成自动化用例。
8. 也可以在“用户测试用例”列表，选择多个用例，批量生成自动化用例，支持多线程。
9. 大模型自动生成的需求功能点、用户测试用例、自动化测试用例，都增加一个修改时间，标记这个数据是否在大模型生成后修改过，并在首页增加统计：可在总数后加括号显示，如：总数（大模型生成数量/修改数量）。

# 问题6：
解决以下问题和功能，中间不用询问，继续完成全部任务并测试验证：

1. 问题：“自动化测试用例”列表的增删改查功能、“生成自动化测试”、“创建自动化测试”按钮的功能都未实现, 列表标题的全选框批量功能未实现。
2. “自动化测试用例”列表点击用例名称连接，可跳转到用例详情页面。
3. “自动化测试用例”的详情页面，以友好YAML格式展示script属性值
4. 大模型自动生成的需求功能点、用户测试用例、自动化测试用例，都增加一个修改时间，标记这个数据是否在大模型生成后修改过，并在首页增加统计：可在总数后加括号显示，如：总数（大模型生成数量/修改数量）。
5. 在"生成测试用例"对话框，选择"生成自动化测试用例"时，会先生成“用户测试用例"，再根据"功能测试类型"的用例生成自动化用例。

# 问题7
解决以下问题和功能，中间不用询问，继续完成全部任务并测试验证：

1. 所有列表页面的过滤条件框，输入之后回车无反应,应该执行过滤查询
2. 前端界面在经过nginx gateway调用后端服务时出现：gateway timeout错误，请修改耗时较长的服务调用为异步请求和响应的方式，包括：生成用户测试用例、生成自动化测试用例、提取需求功能点、导入文档到知识库

# 改进8
解决以下问题和功能实现，中间不用询问，继续完成全部任务并测试验证：
1. 功能点列表的每行增加生成测试用例功能，可对当前行功能生成测试用例。
2. 根据功能点生成测试用例时可以选择生成哪些测试用例类型（可多选），如：UI测试、接口测试、系统测试、性能测试、安全测试。
   - UI测试：通过界面验证功能的操作步骤、预期结果等。
   - 接口测试：通过API验证功能的操作步骤、预期结果等。
   - 系统测试：执行系统命令，验证功能点是否正常。
   - 性能测试：根据功能点生成性能测试用例，如：并发数、请求数、响应时间等。
   - 安全测试：根据功能点生成安全测试用例，如：数据泄露、越权访问、注入攻击等。
   - 每一类测试使用不同的系统提示词，请优化现有的系统提示词，并增加新的系统提示词，确保每个提示词只能生成特定类型的测试用例。
3. midscenejs的自动化测试用例只能根据UI测试生成
4. 使用独立界面管理系统提示词，放在系统管理的子菜单下
5. 功能点列表的功能点增加用例反查功能，可以弹出窗口，列表查看根据该功能点生成的测试用例列表,并可查看用例详情（参考测试用例功能）。

# 改进9： REST API服务
解决以下问题和功能实现，中间不用询问，继续完成全部任务并测试验证：
1. 按照docs/api_req.json的内容完善需求功能点的数据结构：补充缺失的属性，意义相同的属性则修改名称。并优化大模型提取功能点的系统提示词。
2. 按照docs/api_resp.json的内容完善测试用例的数据结构：补充缺失的属性，意义相同的属性则修改名称。并优化大模型生成测试用例的系统提示词。
3. 把UI测试名称改为：功能测试，也就是UI测试等同是功能测试。
4. generate_test_cases_from_requirements()方法生成用例的大模型用户需求内容要包括docs/api_req.json中的内容
5. 增加独立的python文件，提供“根据需求生成测试用例的REST API服务”能力：
  - API端点：/aitest/v1/generate_test_cases
  - 调用test_manager.py的 generate_test_cases_from_requirements()方法
  - 输入格式 docs/api_req.json，并补充一个project_id属性，用于关联知识库检索。
  - 输出格式 docs/api_resp.json，并补充关联需求编号的属性
  - 输出使用实例，补充到README.MD中

改进：
1. 只保留新的字段名称，不用保留旧字段的兼容
2. static/js/test_mananger.js中的“生成用例”的任务进度框不更新，任务实际执行完毕也没有关闭对话框，在任务管理列表里是正常可以看到任务进度的。同样的“生成用例”任务，在templates/requirements/index.html中可以显示进度和正常关闭。
3. REST API服务的请求body中，project_id和test_types放在每个请求中，去掉use_knowledge_base、max_threads（使用默认值）, 请求为如下格式：
```json
[
      {
        "project_id": "test-project-001",
        "number": "REQ-001",
        "title": "用户登录功能",
        "spec": "用户可以通过用户名和密码登录系统",
        "businessProcess": "1.打开登录页面 2.输入用户名密码 3.点击登录按钮 4.验证登录结果",
        "constraints": "用户名长度3-20字符，密码长度6-20字符",
        "verify": "登录成功后跳转到主页，显示用户信息",
        "comment": "支持记住密码功能",
        "test_types": ["functional"],
      }
]

```
响应格式调整为：
```json
{
  "success": true,
  "errros": "如果有错误，返回错误信息，否则空",
  "test_cases": [
      {
        "name": "用户登录功能验证",
        "precondition": "系统已部署并启动；用户账号已创建",
        "stepsJson": "[\"1. 打开登录页面\", \"2. 输入用户名\", \"3. 输入密码\", \"4. 点击登录按钮\"]",
        "expectsJson": "[\"登录成功\", \"跳转到主页\", \"显示用户信息\"]",
        "keywords": "登录,用户验证,功能测试",
        "pri": "1",
        "type": "functional",
        "auto": "auto",
        "distinguish": "0",
        "executionHours": "0.5",
        "stage": "功能测试阶段",
        "req_number": "关联的需求编号",
        "project_id": "test-project-001"
      }
  ]
}
```

# 改进10
解决以下问题和功能实现，中间不用询问，继续完成全部任务并测试验证：

1. 测试用例详情页：类型根据英文枚举值显示为中文，关联需求移到与状态一行，并显示为需求的title并可以通过链接跳转到需求详情页
2. 参考app.py的  def generate_test_cases(): 方法，实现一个相同功能的异步REST API服务：先创建一个任务，然后返回任务ID，用户可以通过任务ID查询任务进度和结果, 任务执行结果保存到data/results目录下，有效期1天。在readme.md中说明这个异步rest的使用说明。
3. 增加一个新的功能菜单：接口项目管理功能。
4. 接口项目管理的功能：
   - 接口项目包括上传接口文档(word/pdf)，默认使用上传的用接口文档名称做为接口项目名称可修改。接口项目可选择语言，默认使用Java。
   - 界面提供接口项目列表管理功能，可以CRUD接口文档
   - 在data/code目录下创建项目目录，存储上传的接口文档
   - 可选中某个接口文档，调用大模型生成接口的单元测试代码，并保存到data/code目录/项目目录下
   - 测试代码生成支持多线程：先解析接口文档拆分出接口分配到线程，每个线程依次调用LLM为每个接口生成单元测试代码
   - 接口的单元测试代码生成也通过任务管理，界面可显示任务执行情况，具体可参考测试用例生成功能。
   - 可以选中某个接口项目，下载整个项目代码（zip包压缩）
# 问题20
1. 新建接口项目成功后，项目列表是空的
2. 新建接口项目的对话框时，应该同时上传接口文档

# claude 终端问题
终端执行“claude -r 79e01bea-0200-4681-9a1d-c5602dfd71de”，只会从控制台输出一次"? for shortcuts"。
claude_session.py的start_session代码使用pty启动的终端执行同样命令，并用def _start_reader(self)读取pty输出，在读完一次pty终端的输出后，再次读取还是会读取到相同的内容。
请解决此问题，并测试检查，并与直接在终端执行的结果进行对比。

运行src/utils/claude_session.py 