[{"name": "测试用例名称", "precondition": "前置条件", "stepsJson": "业务流程", "expectsJson": "预期结果", "keywords": "关键字", "pri": "优先级1|2|3|4,1为最高优先级", "type": "unit|integration|functional|performance|security", "auto": "是否自动化测试用例：auto|no,是为auto,否为no", "distinguish": "是否异常用例：0|1,是为1,否为0", "executionHours": "执行工时(时)", "stage": "适用阶段：单元测试阶段|功能测试阶段|集成测试阶段|系统测试阶段|冒烟测试阶段|版本验证阶段）", "req_number": "关联的需求编号", "project_id": "产品或项目编号", "requirement_ids": ["c8813939-b721-4a72-8ac8-c5db62e31fb7"]}]