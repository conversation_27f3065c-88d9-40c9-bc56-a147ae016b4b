#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
基础功能测试脚本
测试AI智能测试平台的核心功能
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from src.config import Config
from src.storage import StorageManager
from src.utils.llm_client import LLMClient
from src.requirement_manager import RequirementManager
from src.test_manager import TestManager
from src.api_project_manager import ApiProjectManager

def test_gen_test_case():
    print("\n" + "=" * 50)
    print("生成测试用例")
    print("=" * 50)
    
    try:
        config = Config()
        llm_client = LLMClient(config)
        storage = StorageManager(config)
        api_project_manager = ApiProjectManager(storage, config)
        api_project_id = "a96ca61c-c234-49b0-8603-c8c1cd2f9771"
                
        def update_progress(progress: int, message: str=''):
            print(f"进度: {progress}% {message}")
                    
        result = api_project_manager.generate_test_codes_with_claude(api_project_id, update_progress)
        if result["success"]:
            print(f"✓ 测试用例生成结果: {result}")
        else:
            print(f"✗ 测试用例失败: {result}")

    except Exception as e:
        print(f"✗ 测试用例失败: {e}")
        import traceback
        traceback.print_exc()
        return False
def main():
    """主测试函数"""
    test_results = []
    
    # 运行各项测试
    #test_results.append(("需求功能提取", test_requirement_extract()))
    #test_results.append(("测试用例生成", test_make_case()))
    test_results.append(("生成测试用例", test_gen_test_case()))
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
