#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import uuid
from datetime import datetime
from flask import Flask, render_template, request, jsonify, session, redirect, url_for, send_from_directory
from flask_cors import CORS
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 导入模块
from src.config import Config
from src.storage import StorageManager
from src.project_manager import ProjectManager
from src.document_manager import DocumentManager
from src.requirement_manager import RequirementManager
from src.knowledge_manager import KnowledgeManager
from src.test_manager import TestManager
from src.system_config import SystemConfigManager
from src.async_task_manager import get_task_manager
from src.api_project_manager import ApiProjectManager

def create_app():
    app = Flask(__name__)
    app.secret_key = os.urandom(24)
    
    # 启用CORS
    CORS(app)
    
    # 初始化配置
    config = Config()
    app.config.update(config.get_all())
    
    # 初始化存储管理器
    storage = StorageManager(config)
    
    # 初始化各个管理器
    document_manager = DocumentManager(storage, config)
    requirement_manager = RequirementManager(storage, config)
    knowledge_manager = KnowledgeManager(storage, config)
    project_manager = ProjectManager(storage, knowledge_manager)
    test_manager = TestManager(storage, config)
    system_config_manager = SystemConfigManager(storage, config)
    api_project_manager = ApiProjectManager(storage, config)
    
    # 注册蓝图
    from src.routes.project_routes import create_project_blueprint
    from src.routes.document_routes import create_document_blueprint
    from src.routes.requirement_routes import create_requirement_blueprint
    from src.routes.knowledge_routes import create_knowledge_blueprint
    from src.routes.test_routes import create_test_blueprint
    from src.routes.system_routes import create_system_blueprint
    from src.routes.async_task_routes import create_async_task_blueprint
    from src.routes.api_project_routes import create_api_project_blueprint

    app.register_blueprint(create_project_blueprint(project_manager))
    app.register_blueprint(create_document_blueprint(document_manager))
    app.register_blueprint(create_requirement_blueprint(requirement_manager))
    app.register_blueprint(create_knowledge_blueprint(knowledge_manager))
    app.register_blueprint(create_test_blueprint(test_manager))
    app.register_blueprint(create_system_blueprint(system_config_manager))
    app.register_blueprint(create_async_task_blueprint())
    app.register_blueprint(create_api_project_blueprint(api_project_manager))
    
    @app.route('/aitest/')
    def index():
        """主页"""
        projects = project_manager.list_projects()
        return render_template('index.html', projects=projects)
    
    @app.route('/aitest/api/current_project')
    def get_current_project():
        """获取当前选中的项目"""
        return jsonify({'project_id': session.get('current_project_id')})
    
    @app.route('/aitest/api/set_current_project', methods=['POST'])
    def set_current_project():
        """设置当前项目"""
        if not request.is_json:
            return jsonify({'success': False, 'error': 'Missing JSON in request'}), 400

        try:
            data = request.get_json()
        except Exception as e:
            return jsonify({'success': False, 'error': str(e)}), 400

        project_id = data.get('project_id')
        if project_id is None:
            return jsonify({'success': False, 'error': 'Missing project_id in JSON'}), 400

        session['current_project_id'] = project_id
        return jsonify({'success': True})

    @app.route('/aitest/tasks/')
    def tasks():
        """任务管理页面"""
        return render_template('tasks/index.html')
    # 静态文件路由 - 新增的带前缀访问
    @app.route('/aitest/static/<path:filename>')
    def custom_static(filename):
        return send_from_directory('static', filename)
    
    @app.route('/aitest/api/generate_test_cases', methods=['POST'])
    def generate_test_cases():
        """根据需求生成测试用例的API端点"""
        try:
            # 获取请求数据 - 现在是需求数组格式
            requirements_data = request.get_json()
            if not requirements_data:
                return jsonify({
                    'success': False,
                    'errors': '请求数据不能为空',
                    'test_cases': []
                }), 400

            if not isinstance(requirements_data, list):
                return jsonify({
                    'success': False,
                    'errors': '请求数据应为需求数组格式',
                    'test_cases': []
                }), 400

            if len(requirements_data) == 0:
                return jsonify({
                    'success': False,
                    'errors': '需求数组不能为空',
                    'test_cases': []
                }), 400           
            
            # 使用默认值
            use_knowledge_base = True
            max_threads = config.MAX_THREADS
            
            # 调用测试管理器生成测试用例
            result = test_manager.generate_test_cases_from_requirements(
                requirements=requirements_data,
                use_knowledge_base=use_knowledge_base,
                max_threads=max_threads,
                auto_saved=False
            )
            
            if result['success']:
                return jsonify({
                    'success': True,
                    'errors': '',
                    'test_cases': result['test_cases']
                })
            else:
                return jsonify({
                    'success': False,
                    'errors': result.get('errors', '生成测试用例失败'),
                    'test_cases': []
                }), 500
                
        except Exception as e:
            return jsonify({
                'success': False,
                'errors': f'服务器内部错误: {str(e)}',
                'test_cases': []
            }), 500

    @app.route('/aitest/api/async/generate_test_cases', methods=['POST'])
    def async_generate_test_cases():
        """异步生成测试用例的API端点"""
        try:
            # 获取请求数据 - 现在是需求数组格式
            requirements_data = request.get_json()
            if not requirements_data:
                return jsonify({
                    'success': False,
                    'errors': '请求数据不能为空',
                    'task_id': None
                }), 400

            if not isinstance(requirements_data, list):
                return jsonify({
                    'success': False,
                    'errors': '请求数据应为需求数组格式',
                    'task_id': None
                }), 400

            if len(requirements_data) == 0:
                return jsonify({
                    'success': False,
                    'errors': '需求数组不能为空',
                    'task_id': None
                }), 400

            # 使用默认值
            use_knowledge_base = True
            max_threads = config.MAX_THREADS

            # 获取任务管理器
            task_manager = get_task_manager()

            # 提交异步任务
            task_id = task_manager.submit_task(
                task_type='generate_test_cases',
                task_name=f'生成测试用例 - {len(requirements_data)}个需求',
                func=test_manager.generate_test_cases_from_requirements,
                kwargs={
                    'requirements': requirements_data,
                    'use_knowledge_base': use_knowledge_base,
                    'max_threads': max_threads,
                    'auto_saved': False
                }
            )

            return jsonify({
                'success': True,
                'task_id': task_id,
                'message': '任务已创建，请使用task_id查询进度和结果'
            })

        except Exception as e:
            return jsonify({
                'success': False,
                'errors': f'服务器内部错误: {str(e)}',
                'task_id': None
            }), 500

    return app


if __name__ == '__main__':
    app = create_app()
    app.run(
        host=os.getenv('APP_HOST', '0.0.0.0'),
        port=int(os.getenv('APP_PORT', 5000)),
        debug=os.getenv('APP_DEBUG', 'True').lower() == 'true'
    )
